import requests
import json
import os
from dotenv import load_dotenv

# Construct an absolute path to the .env file
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

BASE_URL = os.getenv("BASE_URL")
if not BASE_URL:
    raise ValueError(f"BASE_URL not found in {dotenv_path}")


def make_phone_call(customer_id: int, phone_number: str):
    """
    Initiates a phone call to the given customer and phone number.
    """
    # The BASE_URL from .env is expected to contain the full protocol and domain
    url = f"https://{BASE_URL}/api/voice/outbound-call"
    payload = {
        "customer_id": customer_id,
        "phone_number": phone_number,
        "custom_prompt": "Hello, this is a test call from the script.",
        "custom_first_message": "This is the first message of the test call, which should be spoken by Eleven<PERSON><PERSON><PERSON>.",
    }
    headers = {"Content-Type": "application/json"}

    try:
        print(f"Attempting to call {url}...")
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        print("Phone call initiated successfully:")
        print(response.json())
    except requests.exceptions.RequestException as e:
        print(f"Error making phone call: {e}")
        if e.response:
            print(f"Response status: {e.response.status_code}")
            print(f"Response text: {e.response.text}")


if __name__ == "__main__":
    # Replace with a valid customer ID and phone number
    CUSTOMER_ID = 1
    PHONE_NUMBER = "+41798766832"  # Use a real phone number to test

    print("--- Initiating Phone Call ---")
    make_phone_call(CUSTOMER_ID, PHONE_NUMBER)
