import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import { voiceAPI } from '../utils/api';
import LoadingSpinner from '../components/LoadingSpinner';
import { formatDateTime, formatDuration } from '../utils/helpers';
import { Phone, User, Clock, FileText } from 'lucide-react';
import ConversationLog from '../components/ConversationLog';

export default function CallDetail() {
  const { id } = useParams();
  const { data: call, isLoading, error } = useQuery(
    ['callHistory', id],
    () => voiceAPI.getCallDetails(id).then(res => res.data)
  );

  if (isLoading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500">Error loading call details.</div>;
  if (!call) return <div>Call not found.</div>;

  const renderNotes = (notes) => {
    if (!notes) return 'Not available.';
    try {
      // If notes is a JSON string, parse and stringify it prettily
      const parsed = JSON.parse(notes);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      // If it's not a JSON string, just display it as is
      return notes;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Call Details</h1>
        <p className="mt-1 text-sm text-gray-500">
          Review the details and conversation log of a past call.
        </p>
      </div>

      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-gray-500">Customer</p>
                <p className="font-medium">{call.customer?.name || 'N/A'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-gray-500">Contact</p>
                <p className="font-medium">{call.phone_number || call.call_type}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-gray-500">Duration</p>
                <p className="font-medium">{formatDuration(call.call_duration)}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-gray-500">Status</p>
                <p className="font-medium capitalize">{call.status}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-gray-500">Date</p>
                <p className="font-medium">{formatDateTime(call.created_at)}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Initial Prompts</h3>
          <div className="space-y-4 text-sm">
            <div>
              <p className="text-gray-500 font-medium">System Prompt</p>
              <p className="mt-1 text-gray-800 bg-gray-50 rounded-md p-3 whitespace-pre-wrap">
                {call.system_prompt || 'Not available.'}
              </p>
            </div>
            <div>
              <p className="text-gray-500 font-medium">First Message</p>
              <p className="mt-1 text-gray-800 bg-gray-50 rounded-md p-3 whitespace-pre-wrap">
                {call.first_message || 'Not available.'}
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Notes</h3>
          <pre className="mt-1 text-sm text-gray-800 bg-gray-50 rounded-md p-3 whitespace-pre-wrap">
            {renderNotes(call.customer?.notes)}
          </pre>
        </div>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Call Enrichment</h3>
          <pre className="mt-1 text-sm text-gray-800 bg-gray-50 rounded-md p-3 whitespace-pre-wrap">
            {JSON.stringify(call.customer?.enrichment, null, 2) || 'Not available.'}
          </pre>
        </div>
        <div className="p-6">
          <ConversationLog log={call.conversation_log} />
        </div>
      </div>
    </div>
  );
}