import json
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from .. import crud, schemas
from ..database import get_db
from ..services.langchain_service import LangchainAgentService  # Corrected import

router = APIRouter(prefix="/chat", tags=["chat"])


class DirectChatRequest(BaseModel):
    message: str
    customer_id: Optional[int] = None


class DirectChatResponse(BaseModel):
    response: str


@router.post("/direct", response_model=DirectChatResponse)
async def direct_chat(request: DirectChatRequest, db: Session = Depends(get_db)):
    """
    Allows direct chat with the LLM, optionally providing customer context.
    """
    messages = []
    system_prompt = "You are a helpful AI assistant. Respond concisely and accurately."
    llm_agent_service = LangchainAgentService()  # Instantiate LLM service

    if request.customer_id:
        customer = crud.get_customer(db, customer_id=request.customer_id)
        if customer:
            # Fetch customer's active cart for context
            cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
            cart_summary = "No items in cart."
            if cart and cart.items:
                item_descriptions = []
                for item in cart.items:
                    desc = f"{item.quantity}x {item.product.name}"
                    if item.size:
                        desc += f" (Size: {item.size})"
                    if item.color:
                        desc += f" (Color: {item.color})"
                    item_descriptions.append(desc)
                cart_summary = f"Customer's cart contains: {', '.join(item_descriptions)}. Total: ${cart.total_amount:.2f}."

            system_prompt = (
                f"You are a helpful AI assistant for a sales company. "
                f"The current customer is {customer.name} (ID: {customer.id}). "
                f"Their email is {customer.email} and phone is {customer.phone}. "
                f"Customer notes: {customer.notes or 'None'}. "
                f"Call enrichment data: {customer.enrichment or 'None'}. "
                f"{cart_summary} "
                f"You have access to tools to assist with customer and product queries, and cart management. "
                f"Use these tools when appropriate to answer questions or fulfill requests. "
                f"Respond concisely and accurately."
            )

    messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": request.message})

    full_response_content = ""
    try:
        # The Langchain agent handles tool calling internally
        async for chunk in llm_agent_service.run_agent(
            messages=messages, db=db, stream=True
        ):
            if "content" in chunk:
                full_response_content += chunk["content"]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM chat error: {e}")

    return DirectChatResponse(response=full_response_content)


class InitiateChatRequest(BaseModel):
    customer_id: Optional[int] = None


class InitiateChatResponse(BaseModel):
    response: str


@router.post("/initiate", response_model=InitiateChatResponse)
async def initiate_chat(request: InitiateChatRequest, db: Session = Depends(get_db)):
    """
    Initiates a chat with the LLM, providing an initial greeting based on customer context.
    """
    messages = []
    system_prompt = "You are a helpful AI assistant. Respond concisely and accurately."
    initial_greeting = "Hello! How can I help you today?"
    llm_agent_service = LangchainAgentService()  # Instantiate LLM service

    if request.customer_id:
        customer = crud.get_customer(db, customer_id=request.customer_id)
        if customer:
            cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
            cart_summary = "No items in cart."
            if cart and cart.items:
                item_descriptions = []
                for item in cart.items:
                    desc = f"{item.quantity}x {item.product.name}"
                    if item.size:
                        desc += f" (Size: {item.size})"
                    if item.color:
                        desc += f" (Color: {item.color})"
                    item_descriptions.append(desc)
                cart_summary = f"Customer's cart contains: {', '.join(item_descriptions)}. Total: ${cart.total_amount:.2f}."

            system_prompt = (
                f"You are a helpful AI assistant for a sales company. "
                f"The current customer is {customer.name} (ID: {customer.id}). "
                f"Their email is {customer.email} and phone is {customer.phone}. "
                f"Customer notes: {customer.notes or 'None'}. "
                f"Call enrichment data: {customer.enrichment or 'None'}. "
                f"{cart_summary} "
                f"You have access to tools to assist with customer and product queries, and cart management. "
                f"Use these tools when appropriate to answer questions or fulfill requests. "
                f"Respond concisely and accurately."
            )
            initial_greeting = f"Hello {customer.name}! How can I help you today?"

    messages.append({"role": "system", "content": system_prompt})
    messages.append(
        {"role": "assistant", "content": initial_greeting}
    )  # LLM's first message

    full_response_content = ""
    try:
        async for chunk in llm_agent_service.run_agent(
            messages=messages, db=db, stream=True
        ):
            if "content" in chunk:
                full_response_content += chunk["content"]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM chat initiation error: {e}")

    return InitiateChatResponse(response=full_response_content)
