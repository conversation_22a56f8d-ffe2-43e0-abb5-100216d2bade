import React, { useEffect, useRef, useState } from 'react';
import { MessageCir<PERSON>, Bot, User, Volume2, Copy, Send } from 'lucide-react';
import { formatDateTime, copyToClipboard } from '../utils/helpers';
import toast from 'react-hot-toast';

export default function LiveTranscript({ conversationLog, isActive, isTextChatActive, onSendMessage }) {
  const transcriptEndRef = useRef(null);
  const scrollContainerRef = useRef(null);
  const [userInput, setUserInput] = useState('');

  const handleCopyTranscript = async () => {
    const transcriptText = conversationLog
      .filter(entry => entry.event_type === 'user_message' || entry.event_type === 'agent_message')
      .map(entry => `[${entry.timestamp}] ${entry.source}: ${entry.data.content}`)
      .join('\n');

    const success = await copyToClipboard(transcriptText);
    if (success) {
      toast.success('Transcript copied to clipboard');
    } else {
      toast.error('Failed to copy transcript');
    }
  };

  const handleSendMessage = () => {
    if (!userInput.trim()) return;
    onSendMessage(userInput);
    setUserInput('');
  };

  const transcript = conversationLog.filter(entry => 
    entry.event_type === 'user_message' || 
    entry.event_type === 'agent_message' ||
    entry.event_type === 'transcript'
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900">Live Transcript</h3>
            {isActive && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600">Live</span>
              </div>
            )}
          </div>
          {transcript.length > 0 && (
            <button
              onClick={handleCopyTranscript}
              className="btn btn-ghost btn-sm"
              title="Copy transcript"
            >
              <Copy className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      <div ref={scrollContainerRef} className="flex-grow min-h-96 max-h-[480px] overflow-y-auto p-4" style={{ maxHeight: 'calc(1 * 24rem)' }}>
        {transcript.length > 0 ? (
          <div className="space-y-4">
            {transcript.map((entry, index) => {
              const speaker = entry.source === 'user' ? 'user' : 'agent';
              const text = entry.data.content;
              const timestamp = entry.timestamp;

              return (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${speaker === 'agent'
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600'
                    }`}>
                    {speaker === 'agent' ? (
                      <Bot className="h-4 w-4" />
                    ) : (
                      <User className="h-4 w-4" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`text-sm font-medium ${speaker === 'agent' ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                        {speaker === 'agent' ? 'AI Agent' : 'Customer'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatDateTime(timestamp)}
                      </span>
                    </div>
                    <div className={`text-sm p-3 rounded-lg ${speaker === 'agent'
                      ? 'bg-blue-50 text-blue-900'
                      : 'bg-gray-50 text-gray-900'
                      }`}>
                      {text}
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={transcriptEndRef} />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              {isActive ? 'Waiting for conversation...' : 'No transcript available'}
            </h4>
            <p className="text-sm text-gray-500">
              {isActive
                ? 'The conversation transcript will appear here in real-time'
                : 'Start a call or chat to see the live transcript'
              }
            </p>
          </div>
        )}
      </div>

      {/* Transcript Input */}
      {isTextChatActive && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type your message..."
              className="input input-bordered w-full"
            />
            <button
              onClick={handleSendMessage}
              className="btn btn-primary"
              disabled={!userInput.trim()}
            >
              <Send className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
