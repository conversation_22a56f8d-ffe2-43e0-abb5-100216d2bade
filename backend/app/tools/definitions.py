from google.generativeai.types import FunctionDeclaration, Tool

# Define tool specifications for the LLM
# These mirror the FastAPI endpoints in routers/webhooks.py

get_cart_summary_func = FunctionDeclaration(
    name="get_cart_summary",
    description="Get a summary of the customer's active shopping cart.",
    parameters={
        "type": "object",
        "properties": {
            "customer_id": {"type": "integer"},
        },
        "required": ["customer_id"],
    },
)

search_products_func = FunctionDeclaration(
    name="search_products",
    description="Search for products using a query or category, returning a summarized result.",
    parameters={
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Search query for products (e.g., 't-shirt', 'shoes').",
            },
            "category": {
                "type": "string",
                "description": "Filter products by category (e.g., 'electronics', 'apparel').",
            },
        },
        "required": [],
    },
)

add_cart_item_webhook_func = FunctionDeclaration(
    name="add_cart_item_webhook",
    description="Add an item to the customer's cart using product name.",
    parameters={
        "type": "object",
        "properties": {
            "customer_id": {"type": "integer"},
            "product_name": {
                "type": "string",
                "description": "Name of the product to add.",
            },
            "quantity": {"type": "integer"},
            "size": {"type": "string"},
            "color": {"type": "string"},
        },
        "required": ["customer_id", "product_name"],
    },
)

remove_cart_item_func = FunctionDeclaration(
    name="remove_cart_item",
    description="Remove an item from the customer's cart using product name.",
    parameters={
        "type": "object",
        "properties": {
            "customer_id": {"type": "integer"},
            "product_name": {
                "type": "string",
                "description": "Name of the product to remove.",
            },
            "quantity": {"type": "integer"},
        },
        "required": ["customer_id", "product_name"],
    },
)

checkout_cart_func = FunctionDeclaration(
    name="checkout_cart",
    description="Process the customer's active cart checkout.",
    parameters={
        "type": "object",
        "properties": {
            "customer_id": {"type": "integer"},
        },
        "required": ["customer_id"],
    },
)


def get_tools():
    """
    Returns the list of tools for the LLM.
    """
    return [
        Tool(
            function_declarations=[
                get_cart_summary_func,
                search_products_func,
                add_cart_item_webhook_func,
                remove_cart_item_func,
                checkout_cart_func,
            ]
        )
    ]
