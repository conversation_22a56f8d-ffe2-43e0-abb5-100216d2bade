import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  ArrowLeft,
  Edit,
  Package,
  Tag,
  DollarSign,
  Warehouse,
  Ruler,
  Weight,
  Palette,
  Hash,
  Phone,
} from 'lucide-react';
import { productAPI } from '../utils/api';
import { formatCurrency, formatDateTime } from '../utils/helpers';
import LoadingSpinner from '../components/LoadingSpinner';
import ProductModal from '../components/ProductModal';
import WebCallWidget from '../components/WebCallWidget';

export default function ProductDetail() {
  const { id } = useParams();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCallWidget, setShowCallWidget] = useState(false);

  // Fetch product details
  const { data: product, isLoading, refetch } = useQuery(
    ['product', id],
    () => productAPI.getById(id),
    {
      select: (response) => response.data ?? {},
    }
  );

  const handleProductSaved = () => {
    setShowEditModal(false);
    refetch();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Product not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The product you're looking for doesn't exist.
        </p>
        <div className="mt-6">
          <Link to="/products" className="btn btn-primary btn-md">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/products"
            className="btn btn-ghost btn-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-sm text-gray-500">Product Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowCallWidget(true)}
            className="btn btn-primary btn-md"
          >
            <Phone className="h-4 w-4 mr-2" />
            Talk to Assistant
          </button>
          <button
            onClick={() => setShowEditModal(true)}
            className="btn btn-secondary btn-md"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Product
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Image and Gallery */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-100 mb-4">
              {product.image_url ? (
                <img
                  src={product.image_url}
                  alt={product.name}
                  className="h-full w-full object-cover object-center"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center">
                  <Package className="h-16 w-16 text-gray-400" />
                </div>
              )}
            </div>

            {/* Additional Images */}
            {product.additional_images && product.additional_images.length > 0 && (
              <div className="grid grid-cols-3 gap-2">
                {product.additional_images.map((imageUrl, index) => (
                  <div key={index} className="aspect-w-1 aspect-h-1 overflow-hidden rounded-md bg-gray-100">
                    <img
                      src={imageUrl}
                      alt={`${product.name} ${index + 1}`}
                      className="h-full w-full object-cover object-center"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Product Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{product.name}</h2>
                <div className="flex items-center space-x-4 mt-2">
                  {product.category && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Tag className="h-4 w-4 mr-1" />
                      {product.category}
                    </div>
                  )}
                  {product.brand && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Package className="h-4 w-4 mr-1" />
                      {product.brand}
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900">
                  {formatCurrency(product.price)}
                </p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${product.is_active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                  }`}>
                  {product.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            {product.description && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600">{product.description}</p>
              </div>
            )}

            {product.product_enrichment && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Product Enrichment</h3>
                <p className="text-gray-600">{product.product_enrichment}</p>
              </div>
            )}

            {/* Product Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {product.sku && (
                  <div className="flex items-center space-x-3">
                    <Hash className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">SKU</p>
                      <p className="text-sm text-gray-600">{product.sku}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Warehouse className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Stock Quantity</p>
                    <p className={`text-sm font-medium ${product.stock_quantity > 10 ? 'text-green-600' :
                        product.stock_quantity > 0 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                      {product.stock_quantity} units
                    </p>
                  </div>
                </div>

                {product.weight && (
                  <div className="flex items-center space-x-3">
                    <Weight className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Weight</p>
                      <p className="text-sm text-gray-600">{product.weight} lbs</p>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {product.dimensions && (
                  <div className="flex items-center space-x-3">
                    <Ruler className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Dimensions</p>
                      <p className="text-sm text-gray-600">
                        {product.dimensions.length}" × {product.dimensions.width}" × {product.dimensions.height}"
                      </p>
                    </div>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-900 mb-1">Created</p>
                  <p className="text-sm text-gray-600">{formatDateTime(product.created_at)}</p>
                </div>

                {product.updated_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Last Updated</p>
                    <p className="text-sm text-gray-600">{formatDateTime(product.updated_at)}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Variants */}
          {((product.sizes && product.sizes.length > 0) || (product.colors && product.colors.length > 0)) && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Product Variants</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {product.sizes && product.sizes.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Available Sizes</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.sizes.map((size) => (
                        <span
                          key={size}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                        >
                          {size}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {product.colors && product.colors.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Available Colors</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.colors.map((color) => (
                        <span
                          key={color}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                        >
                          <Palette className="h-3 w-3 mr-1" />
                          {color}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tags */}
          {product.tags && product.tags.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Call Enrichment */}
          {product.enrichment && Object.keys(product.enrichment).length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Call Enrichment Data</h3>
              <div className="space-y-3">
                {Object.entries(product.enrichment).map(([key, value]) => (
                  <div key={key}>
                    <p className="text-sm font-medium text-gray-700 capitalize">
                      {key.replace(/_/g, ' ')}
                    </p>
                    <p className="text-sm text-gray-600">
                      {Array.isArray(value) ? value.join(', ') : String(value)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Product Modal */}
      {showEditModal && (
        <ProductModal
          product={product}
          onClose={() => setShowEditModal(false)}
          onSave={handleProductSaved}
        />
      )}

      {/* Web Call Widget */}
      {showCallWidget && (
        <WebCallWidget
          productId={product.id}
          onClose={() => setShowCallWidget(false)}
        />
      )}
    </div>
  );
}
