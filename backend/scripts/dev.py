#!/usr/bin/env python3
"""
Development script for running the application with uv
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {cmd}")
        print(f"Error: {e.stderr}")
        sys.exit(1)

def check_python_version():
    """Check if Python 3.12+ is installed"""
    if sys.version_info >= (3, 12):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
        return True
    else:
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} is installed, but Python 3.12+ is required")
        return False

def main():
    """Main development script"""
    backend_dir = Path(__file__).parent.parent
    
    print("🚀 Starting development server with uv...")
    
    # Check Python version
    if not check_python_version():
        print("❌ Python 3.12+ is required")
        sys.exit(1)
    
    # Check if .venv exists
    venv_path = backend_dir / ".venv"
    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        run_command("uv venv", cwd=backend_dir)
    
    # Install dependencies
    print("📥 Installing dependencies...")
    run_command("uv pip install -r requirements.txt", cwd=backend_dir)
    
    # Initialize database if needed
    db_path = backend_dir / "app.db"
    if not db_path.exists():
        print("🗄️ Initializing database...")
        run_command("uv run python init_db.py", cwd=backend_dir)
    
    # Start the server
    print("🚀 Starting FastAPI server...")
    run_command("uv run python main.py", cwd=backend_dir)

if __name__ == "__main__":
    main()
