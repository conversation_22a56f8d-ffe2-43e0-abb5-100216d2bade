import React from 'react';
import { Link } from 'react-router-dom';
import { formatDateTime } from '../utils/helpers';

export default function RecentActivity({ activities = [] }) {
  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No recent activity</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <Link 
          to={activity.link || '#'} 
          key={index} 
          className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50"
        >
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
              <activity.icon className="h-4 w-4 text-primary-600" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm text-gray-900">{activity.description}</p>
            <p className="text-xs text-gray-500 mt-1">
              {formatDateTime(activity.timestamp)}
            </p>
          </div>
        </Link>
      ))}
    </div>
  );
}
