import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.app import app
from app.database import get_db, engine
from app import models, crud
from init_db import init_database
from app.llms import get_llm


@pytest.fixture(scope="module")
def db_session():
    """Fixture to provide a database session for tests, with setup and teardown."""
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)
    init_database()
    db: Session = next(get_db())
    try:
        yield db
    finally:
        db.close()


@pytest.fixture(scope="module")
def client(db_session):
    """Fixture to provide a TestClient instance."""

    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as c:
        yield c
    app.dependency_overrides.clear()


@pytest.fixture(scope="module")
def agent_service():
    """Fixture to provide a LangchainAgentService instance."""
    return get_llm()


async def run_agent_and_get_response(agent_service: LangchainAgentService, messages: list, db: Session):
    """Helper to run the agent and collect the full response."""
    full_response = ""
    async for chunk in agent_service.run_agent(messages=messages, db=db, stream=False):
        if "content" in chunk:
            full_response += chunk["content"]
    return full_response


@pytest.mark.asyncio
async def test_e2e_customer_journey(db_session: Session, agent_service: LangchainAgentService, client: TestClient):
    """
    Tests a full customer journey using the LLM agent:
    1. Get customer details by ID.
    2. Search for a product.
    3. Add the product to the cart.
    4. Get cart summary.
    5. Remove the product from the cart.
    6. Checkout the cart.
    """
    print("\n--- Starting E2E Customer Journey Test ---")

    messages = []
    customer_id = 1

    # Clear the customer's cart before starting the test
    cart = crud.get_customer_active_cart(db_session, customer_id)
    if cart:
        for item in cart["items"]:
            crud.remove_cart_item(db_session, item["id"])

    # 1. Get customer details
    print("\n--- Step 1: Get Customer Details ---")
    get_customer_prompt = f"Can you tell me the details for customer with ID {customer_id}?"
    messages.append({"role": "user", "content": get_customer_prompt})
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})
    assert "John Smith" in response
    assert "<EMAIL>" in response
    print("Customer details retrieved successfully.")

    # 2. Search for a product
    print("\n--- Step 2: Search for Products ---")
    search_prompt = "Great, now can you find me a 'Classic White T-Shirt'?"
    messages.append({"role": "user", "content": search_prompt})
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})
    assert "Classic White T-Shirt" in response

    # 3. Add item to cart
    print("\n--- Step 3: Add Item to Cart ---")
    add_item_prompt = "Perfect. Please add one of those to my cart. Size medium, color white."
    messages.append({"role": "user", "content": add_item_prompt})
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})

    cart_summary = crud.get_cart_summary(db_session, customer_id)
    assert cart_summary is not None
    assert len(cart_summary["items"]) == 1
    assert cart_summary["items"][0]["name"] == "Classic White T-Shirt"
    print("Item added to cart successfully.")

    # 4. Get cart summary
    print("\n--- Step 4: Get Cart Summary ---")
    summary_prompt = "What's in my cart now?"
    messages.append({"role": "user", "content": summary_prompt})
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})
    assert "Classic White T-Shirt" in response
    assert "total" in response.lower()
    print("Cart summary retrieved successfully.")

    # 5. Remove item from cart
    print("\n--- Step 5: Remove Item from Cart ---")
    remove_item_prompt = "Actually, please remove the t-shirt from my cart."
    messages.append({"role": "user", "content": remove_item_prompt})
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})

    cart_summary = crud.get_cart_summary(db_session, customer_id)
    assert cart_summary is None or len(cart_summary.get("items", [])) == 0
    print("Item removed from cart successfully.")

    # 6. Checkout
    print("\n--- Step 6: Checkout ---")
    messages.append(
        {"role": "user", "content": "You know what, add a 'Wireless Headphones' to my cart and then let's checkout."}
    )
    response = await run_agent_and_get_response(agent_service, messages, db_session)
    print(f"Agent Response: {response}")
    messages.append({"role": "assistant", "content": response})

    assert "order is complete" in response.lower() or "order has been placed" in response.lower()

    active_cart = crud.get_customer_active_cart(db_session, customer_id)
    assert active_cart is None

    orders = crud.get_orders_by_customer(db_session, customer_id)
    assert len(orders) > 0
    assert orders[0]["status"] == "completed"
    print("Checkout completed successfully.")

    print("\n--- E2E Customer Journey Test Finished ---")
