import React, { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { PhoneCall, Mic, MessageSquare, Save, Plus, Trash2, Edit3 } from 'lucide-react';
import { promptTestAPI, voiceAPI } from '../utils/api';
import toast from 'react-hot-toast';
import WebCallWidget from './WebCallWidget';
import LiveTranscript from './LiveTranscript';
import Insights from './Insights';
import ConversationLog from './ConversationLog';

export default function PromptTestTab() {
  const [selectedPromptTest, setSelectedPromptTest] = useState(null);
  const [name, setName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [firstMessage, setFirstMessage] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  
  // Call state
  const [activeCall, setActiveCall] = useState(null);
  const [callStatus, setCallStatus] = useState('idle');
  const [webCallStatus, setWebCallStatus] = useState('idle');
  const [textChatStatus, setTextChatStatus] = useState('idle');
  const [conversationLog, setConversationLog] = useState([]);
  const [insights, setInsights] = useState([]);
  const [actions, setActions] = useState([]);
  const [isWebCallWidgetOpen, setIsWebCallWidgetOpen] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  const ws = useRef(null);
  const callUpdatesWs = useRef(null);
  const queryClient = useQueryClient();

  // Fetch all prompt tests
  const { data: promptTests, isLoading } = useQuery(
    'promptTests',
    () => promptTestAPI.getAll(),
    {
      select: (response) => (Array.isArray(response.data) ? response.data : []),
    }
  );

  // Create prompt test mutation
  const createMutation = useMutation(
    (data) => promptTestAPI.create(data),
    {
      onSuccess: (response) => {
        toast.success('Prompt test saved successfully!');
        queryClient.invalidateQueries('promptTests');
        setSelectedPromptTest(response.data);
        setIsEditing(false);
        clearForm();
      },
      onError: () => {
        toast.error('Failed to save prompt test.');
      }
    }
  );

  // Update prompt test mutation
  const updateMutation = useMutation(
    ({ id, data }) => promptTestAPI.update(id, data),
    {
      onSuccess: (response) => {
        toast.success('Prompt test updated successfully!');
        queryClient.invalidateQueries('promptTests');
        setSelectedPromptTest(response.data);
        setIsEditing(false);
      },
      onError: () => {
        toast.error('Failed to update prompt test.');
      }
    }
  );

  // Delete prompt test mutation
  const deleteMutation = useMutation(
    (id) => promptTestAPI.delete(id),
    {
      onSuccess: () => {
        toast.success('Prompt test deleted successfully!');
        queryClient.invalidateQueries('promptTests');
        if (selectedPromptTest?.id === id) {
          setSelectedPromptTest(null);
          clearForm();
        }
      },
      onError: () => {
        toast.error('Failed to delete prompt test.');
      }
    }
  );

  const clearForm = () => {
    setName('');
    setPhoneNumber('');
    setSystemPrompt('');
    setFirstMessage('');
  };

  const handleSave = () => {
    if (!name.trim() || !systemPrompt.trim() || !firstMessage.trim()) {
      toast.error('Please fill in all required fields (Name, System Prompt, First Message).');
      return;
    }

    const data = {
      name: name.trim(),
      phone_number: phoneNumber.trim() || null,
      system_prompt: systemPrompt.trim(),
      first_message: firstMessage.trim(),
    };

    if (isEditing && selectedPromptTest) {
      updateMutation.mutate({ id: selectedPromptTest.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handlePromptTestSelect = (promptTest) => {
    setSelectedPromptTest(promptTest);
    setName(promptTest.name);
    setPhoneNumber(promptTest.phone_number || '');
    setSystemPrompt(promptTest.system_prompt);
    setFirstMessage(promptTest.first_message);
    setIsEditing(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleDelete = (promptTest) => {
    if (window.confirm(`Are you sure you want to delete "${promptTest.name}"?`)) {
      deleteMutation.mutate(promptTest.id);
    }
  };

  const handleNewPromptTest = () => {
    setSelectedPromptTest(null);
    clearForm();
    setIsEditing(false);
  };

  // Call handling functions
  const handlePhoneCall = async () => {
    if (!systemPrompt.trim() || !firstMessage.trim()) {
      toast.error('Please fill in System Prompt and First Message before making a call.');
      return;
    }

    if (!phoneNumber.trim()) {
      toast.error('Please enter a phone number for phone calls.');
      return;
    }

    try {
      const response = await voiceAPI.initiateCall({
        customer_id: 1, // Dummy customer ID for testing
        phone_number: phoneNumber,
        custom_prompt: systemPrompt,
        custom_first_message: firstMessage,
      });

      if (response.data.success) {
        setActiveCall({
          call_history_id: response.data.call_history_id,
          call_sid: response.data.call_sid,
          type: 'phone',
          phone_number: phoneNumber,
        });
        setCallStatus('dialing');
        toast.success('Phone call initiated!');
      }
    } catch (error) {
      toast.error('Failed to initiate phone call.');
    }
  };

  const handleWebCall = async () => {
    if (!systemPrompt.trim() || !firstMessage.trim()) {
      toast.error('Please fill in System Prompt and First Message before making a call.');
      return;
    }

    try {
      const response = await voiceAPI.initiateWebCall({
        customer_id: 1, // Dummy customer ID for testing
        custom_prompt: systemPrompt,
        custom_first_message: firstMessage,
      });

      if (response.data.success) {
        setActiveCall({
          call_history_id: response.data.call_history_id,
          type: 'web',
        });
        setWebCallStatus('connecting');
        setIsWebCallWidgetOpen(true);
        toast.success('Web call initiated!');
      }
    } catch (error) {
      toast.error('Failed to initiate web call.');
    }
  };

  const handleTextChat = async () => {
    if (!systemPrompt.trim() || !firstMessage.trim()) {
      toast.error('Please fill in System Prompt and First Message before starting chat.');
      return;
    }

    try {
      const response = await voiceAPI.initiateTextChat({
        customer_id: 1, // Dummy customer ID for testing
        custom_prompt: systemPrompt,
        custom_first_message: firstMessage,
      });

      if (response.data.success) {
        setActiveCall({
          call_history_id: response.data.call_history_id,
          type: 'text',
        });
        setTextChatStatus('connecting');
        
        // Connect to text chat WebSocket
        const wsUrl = voiceAPI.getTextChatSocketUrl();
        ws.current = new WebSocket(wsUrl);
        
        ws.current.onopen = () => {
          setTextChatStatus('connected');
          const initMessage = {
            type: 'init',
            call_history_id: response.data.call_history_id,
            custom_prompt: systemPrompt,
            custom_first_message: firstMessage,
          };
          ws.current.send(JSON.stringify(initMessage));
        };

        ws.current.onmessage = (event) => {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        };

        toast.success('Text chat started!');
      }
    } catch (error) {
      toast.error('Failed to start text chat.');
    }
  };

  const handleWebSocketMessage = (message) => {
    if (message.type === 'transcript' || message.type === 'human') {
      const newLogEntry = {
        event_type: 'transcript',
        source: message.sender,
        data: { content: message.transcript },
        timestamp: new Date().toISOString(),
      };
      setConversationLog(prev => [...prev, newLogEntry]);
    } else if (message.type === 'insight') {
      setInsights(prev => [...prev, message.insight]);
    } else if (message.type === 'action') {
      setActions(prev => [...prev, message.action]);
    }
  };

  const handleSendTextMessage = (text) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      const message = { type: 'user_message', text };
      ws.current.send(JSON.stringify(message));
      const newLogEntry = {
        event_type: 'transcript',
        source: 'user',
        data: { content: text },
        timestamp: new Date().toISOString(),
      };
      setConversationLog(prev => [...prev, newLogEntry]);
    }
  };

  const handleEndCall = () => {
    if (ws.current) {
      ws.current.close();
    }
    if (callUpdatesWs.current) {
      callUpdatesWs.current.close();
    }
    setActiveCall(null);
    setCallStatus('idle');
    setWebCallStatus('idle');
    setTextChatStatus('idle');
    setIsWebCallWidgetOpen(false);
    setConversationLog([]);
    setInsights([]);
    setActions([]);
  };

  const isCallActive = callStatus !== 'idle' || webCallStatus !== 'idle' || textChatStatus !== 'idle';

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {isWebCallWidgetOpen && activeCall && (
        <WebCallWidget
          customerId={1}
          callHistoryId={activeCall.call_history_id}
          onClose={() => {
            setIsWebCallWidgetOpen(false);
            handleEndCall();
          }}
          onConnect={() => setWebCallStatus('connected')}
          onTranscriptUpdate={handleWebSocketMessage}
          customPrompt={systemPrompt}
          customFirstMessage={firstMessage}
          webhookData="{}"
          conversationLog={conversationLog}
        />
      )}

      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prompt Test</h1>
          <p className="mt-1 text-sm text-gray-500">
            Test your prompts with phone calls, web calls, and text chat without customer selection.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {!isCallActive ? (
            <>
              <button
                onClick={handlePhoneCall}
                disabled={!systemPrompt.trim() || !firstMessage.trim() || !phoneNumber.trim()}
                className="btn btn-success btn-md"
              >
                <PhoneCall className="h-4 w-4 mr-2" />
                Phone Call
              </button>
              <button
                onClick={handleWebCall}
                disabled={!systemPrompt.trim() || !firstMessage.trim()}
                className="btn btn-primary btn-md"
              >
                <Mic className="h-4 w-4 mr-2" />
                Web Call
              </button>
              <button
                onClick={handleTextChat}
                disabled={!systemPrompt.trim() || !firstMessage.trim()}
                className="btn btn-info btn-md"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Text Chat
              </button>
            </>
          ) : (
            <button
              onClick={handleEndCall}
              className="btn btn-danger btn-md"
            >
              End Call
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="lg:col-span-8 space-y-6">
          {/* Prompt Configuration */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Prompt Configuration</h3>
              <div className="flex space-x-2">
                <button
                  onClick={handleNewPromptTest}
                  className="btn btn-secondary btn-sm"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  New
                </button>
                {selectedPromptTest && !isEditing && (
                  <button
                    onClick={handleEdit}
                    className="btn btn-secondary btn-sm"
                  >
                    <Edit3 className="h-4 w-4 mr-1" />
                    Edit
                  </button>
                )}
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="input"
                    placeholder="Test prompt name"
                    disabled={!isEditing && selectedPromptTest}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number (Optional)
                  </label>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="input"
                    placeholder="+1234567890"
                    disabled={!isEditing && selectedPromptTest}
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  System Prompt *
                </label>
                <textarea
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  rows={8}
                  className="textarea"
                  placeholder="You are a helpful AI assistant..."
                  disabled={!isEditing && selectedPromptTest}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Message *
                </label>
                <textarea
                  value={firstMessage}
                  onChange={(e) => setFirstMessage(e.target.value)}
                  rows={3}
                  className="textarea"
                  placeholder="Hello! How can I help you today?"
                  disabled={!isEditing && selectedPromptTest}
                />
              </div>
              
              {(isEditing || !selectedPromptTest) && (
                <div className="flex justify-end">
                  <button
                    onClick={handleSave}
                    disabled={createMutation.isLoading || updateMutation.isLoading}
                    className="btn btn-primary btn-md"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {createMutation.isLoading || updateMutation.isLoading ? 'Saving...' : 'Save'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="lg:col-span-4 space-y-6">
          {/* Saved Prompts */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Saved Prompts</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {promptTests && promptTests.length > 0 ? (
                promptTests.map((promptTest) => (
                  <div
                    key={promptTest.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      selectedPromptTest?.id === promptTest.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handlePromptTestSelect(promptTest)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {promptTest.name}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">
                          {promptTest.phone_number || 'No phone number'}
                        </p>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(promptTest);
                        }}
                        className="text-gray-400 hover:text-red-600 ml-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No saved prompts yet
                </p>
              )}
            </div>
          </div>

          {/* Call Status */}
          {activeCall && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Call Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Type:</span>
                  <span className="text-sm font-medium">
                    {activeCall.type === 'phone' ? 'Phone Call' : 
                     activeCall.type === 'web' ? 'Web Call' : 'Text Chat'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Status:</span>
                  <span className="text-sm font-medium text-green-600">
                    {activeCall.type === 'phone' ? callStatus :
                     activeCall.type === 'web' ? webCallStatus : textChatStatus}
                  </span>
                </div>
                {activeCall.phone_number && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Number:</span>
                    <span className="text-sm font-medium">{activeCall.phone_number}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Insights */}
          <Insights
            insights={insights}
            actions={actions}
            isActive={isCallActive}
          />

          {/* Live Transcript */}
          <LiveTranscript
            conversationLog={conversationLog}
            isActive={isCallActive}
            isTextChatActive={textChatStatus === 'connected'}
            onSendMessage={handleSendTextMessage}
          />

          {/* Conversation Log */}
          {activeCall && conversationLog.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <ConversationLog log={conversationLog} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
