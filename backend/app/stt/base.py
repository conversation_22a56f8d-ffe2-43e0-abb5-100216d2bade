import asyncio
from abc import ABC, abstractmethod


class STTBase(ABC):
    """Abstract base class for speech-to-text services."""

    @abstractmethod
    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
    ) -> None:
        """
        Transcribes audio from a queue and puts the transcript into another queue.

        Args:
            audio_queue: The queue to receive audio chunks from.
            transcript_queue: The queue to put the transcribed text into.
        """
        pass
