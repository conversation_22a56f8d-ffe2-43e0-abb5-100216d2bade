import asyncio
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from fastapi import WebSocketDisconnect

@pytest.mark.asyncio
async def test_web_call_websocket(client: TestClient):
    # First, create a customer and a call history entry to get a valid call_history_id
    customer_response = client.post(
        "/api/customers",
        json={"name": "WebSocket User", "phone": "1112223333", "email": "<EMAIL>"},
    )
    assert customer_response.status_code == 200
    customer_id = customer_response.json()["id"]

    call_response = client.post(
        "/api/voice/outbound-web-call",
        json={"customer_id": customer_id, "custom_prompt": "Test prompt", "custom_first_message": "Hello!"},
    )
    assert call_response.status_code == 200
    call_history_id = call_response.json()["call_history_id"]

    with client.websocket_connect(f"/api/voice/web-call") as websocket:
        # Send init message
        init_data = {
            "type": "init",
            "data": {
                "call_history_id": call_history_id,
                "custom_first_message": "Hello! How can I help you today?"
            }
        }
        websocket.send_json(init_data)

        # Receive the agent's first transcript message
        try:
            message = websocket.receive_json()
            assert message["type"] == "transcript"
            assert message["sender"] == "agent"
            assert message["transcript"] == "Hello! How can I help you today?"
            assert message["is_final"] is True
        except Exception as e:
            pytest.fail(f"Did not receive transcript message: {e}")

        # Receive the agent's first audio message
        try:
            message = websocket.receive_json()
            assert message["type"] == "audio"
            assert "audio" in message
        except Exception as e:
            pytest.fail(f"Did not receive audio message: {e}")
        
        # Close the connection
        websocket.close()
