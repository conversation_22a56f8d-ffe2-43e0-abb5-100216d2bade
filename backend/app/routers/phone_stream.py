import asyncio
import logging
from typing import AsyncGenerator

from fastapi import <PERSON>Rout<PERSON>, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from .. import crud
from ..database import get_db
from ..services.conversation_service import ConversationManager

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/voice", tags=["voice_calls"])


@router.websocket("/phone-relay/{call_history_id}")
async def phone_relay_endpoint(
    websocket: WebSocket, call_history_id: int, db: Session = Depends(get_db)
):
    await websocket.accept()
    logger.info(f"WebSocket connection accepted for phone relay: {call_history_id}")

    conversation_manager = ConversationManager(db)
    session_store = {}

    async def input_handler(ws: WebSocket) -> str | None:
        """Handles incoming messages from Twilio ConversationRelay."""
        try:
            while True:
                message = await ws.receive_json()
                event_type = message.get("type")

                if event_type == "setup":
                    logger.info(f"Twilio setup message received: {message}")
                    session_store["call_sid"] = message.get("callSid")

                elif event_type == "prompt":
                    prompt = message.get("voicePrompt")
                    logger.info(f"Received prompt from Twilio: {prompt}")
                    return prompt

                elif event_type == "interrupt":
                    logger.info("Handling interruption from Twilio.")

                else:
                    logger.warning(f"Unknown message type received: {event_type}")

        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during Twilio input.")
            return None

    async def output_handler(text_chunk_generator: AsyncGenerator[str, None]) -> str:
        """Accumulates text from the generator and sends it to Twilio."""
        full_text = "".join([chunk async for chunk in text_chunk_generator])

        logger.info(f"Sending text to Twilio: {full_text}")
        if full_text:
            await websocket.send_json(
                {
                    "type": "text",
                    "token": full_text,
                    "last": True,
                }
            )
        return full_text

    async def first_message_handler(text: str):
        """Handles the first message of the conversation."""

        async def text_generator():
            yield text

        await output_handler(text_generator())

    async def get_init_message():
        """Fetches initial message data from the database."""
        call_history = crud.get_call_history(db, call_history_id)
        return {
            "call_history_id": call_history_id,
            "custom_prompt": call_history.system_prompt,
            "custom_first_message": call_history.first_message,
            "webhook_data": call_history.call_metadata.get("webhook_data"),
        }

    try:
        await conversation_manager.handle_conversation(
            call_history_id=call_history_id,
            input_handler=input_handler,
            output_handler=output_handler,
            first_message_handler=first_message_handler,
            get_init_message=get_init_message,
            websocket=websocket,
        )
    except Exception as e:
        logger.error(f"Error in phone relay conversation handler: {e}", exc_info=True)
    finally:
        logger.info(f"Closing WebSocket connection for phone relay: {call_history_id}")
        if not websocket.client_state == "DISCONNECTED":
            try:
                await websocket.close()
            except RuntimeError as e:
                logger.warning(f"Ignoring RuntimeError during websocket close: {e}")
