#!/usr/bin/env python3
"""
Database initialization script with sample data
"""

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app import models
import json

# Sample products data
SAMPLE_PRODUCTS = [
    {
        "name": "Classic White T-Shirt",
        "description": "Premium cotton t-shirt in classic white. Perfect for everyday wear with a comfortable fit.",
        "price": 29.99,
        "category": "Clothing",
        "brand": "ComfortWear",
        "sku": "CW-TS-001",
        "sizes": ["XS", "S", "M", "L", "XL", "XXL"],
        "colors": ["White", "Black", "Gray"],
        "image_url": "/images/white-tshirt.jpg",
        "stock_quantity": 100,
        "tags": ["casual", "cotton", "basic", "unisex"],
        "enrichment": {
            "selling_points": ["100% cotton", "pre-shrunk", "comfortable fit"],
            "care_instructions": "Machine wash cold, tumble dry low",
        },
    },
    {
        "name": "Blue Denim Jeans",
        "description": "Comfortable straight-fit denim jeans in classic blue wash with premium quality denim.",
        "price": 79.99,
        "category": "Clothing",
        "brand": "DenimCo",
        "sku": "DC-JN-002",
        "sizes": ["28", "30", "32", "34", "36", "38", "40"],
        "colors": ["Blue", "Dark Blue", "Light Blue"],
        "image_url": "/images/blue-jeans.jpg",
        "stock_quantity": 75,
        "tags": ["denim", "casual", "straight-fit"],
        "enrichment": {
            "selling_points": ["Premium denim", "classic fit", "durable construction"],
            "care_instructions": "Machine wash cold, hang dry",
        },
    },
    {
        "name": "Running Sneakers",
        "description": "Lightweight running shoes with excellent cushioning and support for all-day comfort.",
        "price": 129.99,
        "category": "Footwear",
        "brand": "SportMax",
        "sku": "SM-RS-003",
        "sizes": ["7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12"],
        "colors": ["Black", "White", "Gray", "Blue"],
        "image_url": "/images/running-sneakers.jpg",
        "stock_quantity": 50,
        "tags": ["running", "athletic", "comfortable", "lightweight"],
        "enrichment": {
            "selling_points": ["Advanced cushioning", "breathable mesh", "durable sole"],
            "features": ["Shock absorption", "arch support", "moisture-wicking"],
        },
    },
    {
        "name": "Leather Wallet",
        "description": "Genuine leather bifold wallet with multiple card slots and bill compartment.",
        "price": 49.99,
        "category": "Accessories",
        "brand": "LeatherCraft",
        "sku": "LC-WL-004",
        "colors": ["Brown", "Black", "Tan"],
        "image_url": "/images/leather-wallet.jpg",
        "stock_quantity": 30,
        "tags": ["leather", "wallet", "accessories", "genuine"],
        "enrichment": {
            "selling_points": ["Genuine leather", "RFID blocking", "compact design"],
            "features": ["6 card slots", "2 bill compartments", "ID window"],
        },
    },
    {
        "name": "Wireless Headphones",
        "description": "Premium wireless headphones with noise cancellation and 30-hour battery life.",
        "price": 199.99,
        "category": "Electronics",
        "brand": "AudioTech",
        "sku": "AT-WH-005",
        "colors": ["Black", "White", "Silver"],
        "image_url": "/images/wireless-headphones.jpg",
        "stock_quantity": 25,
        "tags": ["wireless", "headphones", "noise-cancelling", "bluetooth"],
        "enrichment": {
            "selling_points": ["Active noise cancellation", "30-hour battery", "premium sound"],
            "features": ["Bluetooth 5.0", "Quick charge", "Foldable design"],
        },
    },
    {
        "name": "Cotton Hoodie",
        "description": "Comfortable cotton blend hoodie with kangaroo pocket and drawstring hood.",
        "price": 59.99,
        "category": "Clothing",
        "brand": "ComfortWear",
        "sku": "CW-HD-006",
        "sizes": ["XS", "S", "M", "L", "XL", "XXL"],
        "colors": ["Gray", "Black", "Navy", "Maroon"],
        "image_url": "/images/cotton-hoodie.jpg",
        "stock_quantity": 60,
        "tags": ["hoodie", "cotton", "casual", "comfortable"],
        "enrichment": {
            "selling_points": ["Cotton blend", "kangaroo pocket", "adjustable hood"],
            "care_instructions": "Machine wash warm, tumble dry medium",
        },
    },
    {
        "name": "Smartphone Case",
        "description": "Protective silicone case for smartphones with raised edges for screen protection.",
        "price": 19.99,
        "category": "Electronics",
        "brand": "ProtectTech",
        "sku": "PT-SC-007",
        "sizes": ["iPhone 14", "iPhone 15", "Samsung Galaxy S23", "Samsung Galaxy S24"],
        "colors": ["Clear", "Black", "Blue", "Pink"],
        "image_url": "/images/phone-case.jpg",
        "stock_quantity": 80,
        "tags": ["phone case", "protection", "silicone", "smartphone"],
        "enrichment": {
            "selling_points": ["Drop protection", "raised edges", "wireless charging compatible"],
            "features": ["Flexible silicone", "Easy installation", "Precise cutouts"],
        },
    },
]

# Sample customers data
SAMPLE_CUSTOMERS = [
    {
        "name": "John Smith",
        "phone": "+41798766832",
        "email": "<EMAIL>",
        "address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zip_code": "10001",
        "notes": "Prefers email communication",
        "enrichment": {
            "preferences": ["casual wear", "electronics"],
            "previous_purchases": ["t-shirts", "jeans"],
            "communication_style": "direct and friendly",
        },
    },
    {
        "name": "Sarah Johnson",
        "phone": "+1234567891",
        "email": "<EMAIL>",
        "address": "456 Oak Ave",
        "city": "Los Angeles",
        "state": "CA",
        "zip_code": "90210",
        "notes": "Frequent buyer, VIP customer",
        "enrichment": {
            "preferences": ["athletic wear", "accessories"],
            "previous_purchases": ["sneakers", "hoodies"],
            "communication_style": "detailed explanations appreciated",
        },
    },
    {
        "name": "Mike Davis",
        "phone": "+1234567892",
        "email": "<EMAIL>",
        "address": "789 Pine St",
        "city": "Chicago",
        "state": "IL",
        "zip_code": "60601",
        "notes": "Tech enthusiast",
        "enrichment": {
            "preferences": ["electronics", "gadgets"],
            "previous_purchases": ["headphones", "phone cases"],
            "communication_style": "technical details important",
        },
    },
]


def init_database():
    """Initialize database with sample data"""
    print("Initializing database with sample data...")

    # Create all tables
    models.Base.metadata.create_all(bind=engine)

    db = SessionLocal()
    try:
        # Add sample products
        print("Adding sample products...")
        for product_data in SAMPLE_PRODUCTS:
            # Check if product already exists
            existing = db.query(models.Product).filter(models.Product.sku == product_data["sku"]).first()

            if not existing:
                product = models.Product(**product_data)
                db.add(product)

        # Add sample customers
        print("Adding sample customers...")
        for customer_data in SAMPLE_CUSTOMERS:
            # Check if customer already exists
            existing = db.query(models.Customer).filter(models.Customer.phone == customer_data["phone"]).first()

            if not existing:
                customer = models.Customer(**customer_data)
                db.add(customer)
                db.commit()
                db.refresh(customer)

                # Create an active cart for each customer
                cart = models.Cart(customer_id=customer.id)
                db.add(cart)

        db.commit()

        # Add some sample cart items
        print("Adding sample cart items...")
        customers = db.query(models.Customer).limit(2).all()
        products = db.query(models.Product).limit(3).all()

        if customers and products:
            for i, customer in enumerate(customers):
                cart = (
                    db.query(models.Cart)
                    .filter(models.Cart.customer_id == customer.id, models.Cart.status == "active")
                    .first()
                )

                if cart:
                    # Add 1-2 items to each cart
                    for j in range(min(2, len(products))):
                        product = products[j]
                        cart_item = models.CartItem(
                            cart_id=cart.id,
                            product_id=product.id,
                            quantity=1 + j,
                            size=product.sizes[0] if product.sizes else None,
                            color=product.colors[0] if product.colors else None,
                            price=product.price,
                        )
                        db.add(cart_item)

                    # Update cart total
                    cart_items = db.query(models.CartItem).filter(models.CartItem.cart_id == cart.id).all()
                    total = sum(item.price * item.quantity for item in cart_items)
                    cart.total_amount = total

        db.commit()

        print("Database initialization completed successfully!")
        print(f"Added {len(SAMPLE_PRODUCTS)} products and {len(SAMPLE_CUSTOMERS)} customers")

    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    init_database()
