import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Customer API
export const customerAPI = {
  getAll: (params = {}) => api.get('/customers', { params }),
  getById: (id) => api.get(`/customers/${id}`),
  create: (data) => api.post('/customers', data),
  update: (id, data) => api.put(`/customers/${id}`, data),
  delete: (id) => api.delete(`/customers/${id}`),
  getCart: (customerId) => api.get(`/customers/${customerId}/cart`),
  addToCart: (customerId, item) => api.post(`/customers/${customerId}/cart/items`, item),
  updateCartItem: (itemId, data) => api.put(`/customers/cart/items/${itemId}`, data),
  removeCartItem: (itemId) => api.delete(`/customers/cart/items/${itemId}`),
  getCallHistory: (customerId) => api.get(`/customers/${customerId}/call-history`),
  getOrderHistory: (customerId) => api.get(`/customers/${customerId}/order-history`),
  getPromptPreview: (customerId, data) => api.post(`/customers/${customerId}/prompt-preview`, data),
};

// Product API
export const productAPI = {
  getAll: (params = {}) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (data) => api.post('/products', data),
  update: (id, data) => api.put(`/products/${id}`, data),
  delete: (id) => api.delete(`/products/${id}`),
  getCategories: () => api.get('/products/categories/list'),
  getSearchSuggestions: (query, limit = 10) =>
    api.get('/products/search/suggestions', { params: { query, limit } }),
  getEnrichment: (id) => api.get(`/products/${id}/enrichment`),
  updateEnrichment: (id, data) => api.post(`/products/${id}/enrichment`, data),
};

// Voice Call API
export const voiceAPI = {
  initiateCall: (data) => api.post('/voice/outbound-call', data),
  initiateWebCall: (data) => api.post('/voice/outbound-web-call', data),
  initiateTextChat: (data) => api.post('/voice/outbound-text-chat', data),
  getCallStatus: (callSid) => api.get(`/voice/call-status/${callSid}`),
  endCall: (callSid) => api.post(`/voice/end-call/${callSid}`),
  getSignedUrl: (customerId) =>
    api.get('/voice/elevenlabs/signed-url', { params: { customer_id: customerId } }),
  getCallDetails: (callId) => api.get(`/voice/call-history/${callId}`),
  updateCallHistory: (callId, data) => api.post(`/voice/call-history/${callId}`, data),
  getTextChatSocketUrl: () => {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = apiUrl.replace(/^https?:\/\//, '');
    return `${wsProtocol}://${wsUrl}/voice/text-chat-ws`;
  },
  getCallUpdatesSocketUrl: () => {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = apiUrl.replace(/^https?:\/\//, '').replace('/api', '');
    return `${wsProtocol}://${wsUrl}/ws/call-updates`;
  },
};

// Webhook API (for testing)
export const webhookAPI = {
  getCartSummary: (customerId) =>
    api.post('/webhooks/get-cart-summary', { customer_id: customerId }),
  searchProducts: (query, category) =>
    api.post('/webhooks/search-products', { query, category }),
  addCartItem: (customerId, productName, quantity, size) =>
    api.post('/webhooks/add-cart-item', {
      customer_id: customerId,
      product_name: productName,
      quantity,
      size
    }),
  removeCartItem: (customerId, productName, quantity) =>
    api.post('/webhooks/remove-cart-item', {
      customer_id: customerId,
      product_name: productName,
      quantity
    }),
  checkoutCart: (customerId) =>
    api.post('/webhooks/checkout-cart', { customer_id: customerId }),
};

// System API
export const systemAPI = {
  getStatus: () => api.get('/status'),
  getHealth: () => api.get('/health'),
};

export default api;
