import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Async<PERSON><PERSON>ator, <PERSON>wai<PERSON>, Callable

from sqlalchemy.orm import Session

from .. import crud
from ..config import settings
from ..global_websocket_manager import manager as global_ws_manager
from ..schemas import CallHistoryUpdate, ConversationLogEvent
from ..services.langchain_service import LangchainAgentService

logger = logging.getLogger(__name__)


class ConversationManager:
    def __init__(self, db: Session):
        self.db = db
        self.agent_service = LangchainAgentService()
        self.start_time = time.time()

    async def handle_conversation(
        self,
        call_history_id: int,
        input_handler: Callable[[Any], Awaitable[str]],
        output_handler: Callable[[AsyncGenerator[str, None]], Awaitable[str]],
        first_message_handler: Callable[[str], Awaitable[None]],
        get_init_message: Callable[[], Awaitable[dict]],
        tool_handler: Callable[[dict], <PERSON>waitable[None]] = None,
        websocket: Any = None,
    ):
        logger.info(f"Starting conversation for call history ID: {call_history_id}")
        try:
            init_message = await get_init_message()
            logger.info(f"Received init message: {init_message}")

            data = init_message.get("data", init_message)

            call_history = crud.get_call_history(self.db, call_history_id)
            if not call_history:
                raise Exception(f"Call history {call_history_id} not found.")

            call_context = crud.get_call_context(
                self.db, call_history.customer_id, data.get("webhook_data")
            )
            if not call_context:
                raise Exception(
                    f"Could not generate context for customer {call_history.customer_id}"
                )

            system_prompt = call_history.system_prompt or "You are a helpful assistant."
            first_message = (
                call_history.first_message
                or "Hello, {customer_name}! How can I help you today?"
            ).replace(
                "{customer_name}",
                call_context.get("customer_info", {}).get("name", "there"),
            )
            first_message = first_message.replace(
                "{customer_name}",
                call_context.get("customer_info", {}).get("name", "there"),
            )

            messages = [{"role": "system", "content": system_prompt}]

            if first_message:
                await first_message_handler(first_message)
                messages.append({"role": "assistant", "content": first_message})

            while True:
                try:
                    user_message = await input_handler(websocket)
                    if user_message is None:
                        break

                    messages.append({"role": "user", "content": user_message})

                except ValueError as e:
                    logger.warning(f"Skipping message due to input error: {e}")
                    continue

                should_end_conversation = False

                async def llm_response_generator():
                    nonlocal should_end_conversation
                    full_response_text = ""

                    if settings.use_langchain_agent:
                        logger.info("Using Langchain agent for response.")
                        response_stream = self.agent_service.run_agent_async(
                            messages=messages,
                            db=self.db,
                            context=call_context,
                            call_history_id=call_history_id,
                            websocket=websocket,
                        )
                    else:
                        logger.info("Bypassing agent, calling LLM directly.")
                        response_stream = self.agent_service.run_llm_async(
                            messages=messages, context=call_context
                        )

                    async for chunk in response_stream:
                        if "stream_chunk" in chunk and chunk["stream_chunk"]:
                            yield chunk["stream_chunk"]
                            full_response_text += chunk["stream_chunk"]
                        elif "output" in chunk and chunk["output"]:
                            if not full_response_text:
                                yield chunk["output"]
                            full_response_text = chunk["output"]
                        elif "tool_call" in chunk and chunk["tool_call"]:
                            logger.info(f"Handling tool call: {chunk['tool_call']}")
                            if tool_handler:
                                await tool_handler(chunk["tool_call"])

                    end_keywords = ["goodbye", "bye", "thank you for calling"]
                    if any(
                        keyword in full_response_text.lower()
                        for keyword in end_keywords
                    ):
                        should_end_conversation = True

                full_response = await output_handler(llm_response_generator())
                messages.append({"role": "assistant", "content": full_response})

                if should_end_conversation:
                    logger.info("End of conversation detected. Closing connection.")
                    break

        except Exception as e:
            logger.error(f"Error in conversation: {e}", exc_info=True)
        finally:
            duration = int(time.time() - self.start_time)
            update_data = CallHistoryUpdate(call_duration=duration, status="completed")
            crud.update_call_history_details(self.db, call_history_id, update_data)
            self.db.commit()
            logger.info(f"Conversation ended for call history ID: {call_history_id}")
