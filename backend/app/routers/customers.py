from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from .. import crud, models, schemas
from ..database import get_db

router = APIRouter(prefix="/customers", tags=["customers"])


@router.get("", response_model=List[schemas.Customer])
def read_customers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(
        None, description="Search customers by name, email, or phone"
    ),
    db: Session = Depends(get_db),
):
    """
    Retrieve customers with optional search functionality
    """
    customers = crud.get_customers(db, skip=skip, limit=limit, search=search)
    return customers


@router.post("", response_model=schemas.Customer)
def create_customer(customer: schemas.CustomerCreate, db: Session = Depends(get_db)):
    """
    Create a new customer
    """
    # Check if phone number already exists
    db_customer = crud.get_customer_by_phone(db, phone=customer.phone)
    if db_customer:
        raise HTTPException(status_code=400, detail="Phone number already registered")

    return crud.create_customer(db=db, customer=customer)


@router.get("/{customer_id}", response_model=schemas.Customer)
def read_customer(customer_id: int, db: Session = Depends(get_db)):
    """
    Get a specific customer by ID
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    return db_customer


@router.put("/{customer_id}", response_model=schemas.Customer)
def update_customer(
    customer_id: int, customer: schemas.CustomerUpdate, db: Session = Depends(get_db)
):
    """
    Update a customer's information
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Check if phone number is being changed and if it already exists
    if customer.phone and customer.phone != db_customer.phone:
        existing_customer = crud.get_customer_by_phone(db, phone=customer.phone)
        if existing_customer:
            raise HTTPException(
                status_code=400, detail="Phone number already registered"
            )

    return crud.update_customer(db=db, customer_id=customer_id, customer=customer)


@router.delete("/{customer_id}")
def delete_customer(customer_id: int, db: Session = Depends(get_db)):
    """
    Soft delete a customer (mark as inactive)
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    crud.delete_customer(db=db, customer_id=customer_id)
    return {"message": "Customer deleted successfully"}


@router.get("/{customer_id}/cart", response_model=schemas.Cart)
def get_customer_cart(customer_id: int, db: Session = Depends(get_db)):
    """
    Get customer's active cart
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    cart = crud.get_customer_active_cart(db, customer_id=customer_id)
    if cart is None:
        # Create a new cart if none exists
        cart = crud.create_cart(db, schemas.CartCreate(customer_id=customer_id))

    # Calculate and update total
    crud.calculate_cart_total(db, cart.id)

    return cart


@router.post("/{customer_id}/cart/items", response_model=schemas.CartItem)
def add_item_to_cart(
    customer_id: int, item: schemas.CartItemCreate, db: Session = Depends(get_db)
):
    """
    Add an item to customer's cart
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Get or create active cart
    cart = crud.get_customer_active_cart(db, customer_id=customer_id)
    if cart is None:
        cart = crud.create_cart(db, schemas.CartCreate(customer_id=customer_id))

    # Add item to cart
    cart_item = crud.add_cart_item(db, cart_id=cart.id, item=item)
    if cart_item is None:
        raise HTTPException(status_code=404, detail="Product not found")

    # Update cart total
    crud.calculate_cart_total(db, cart.id)

    return cart_item


@router.put("/cart/items/{item_id}", response_model=schemas.CartItem)
def update_cart_item(
    item_id: int, item: schemas.CartItemUpdate, db: Session = Depends(get_db)
):
    """
    Update a cart item
    """
    cart_item = crud.update_cart_item(db, item_id=item_id, item=item)
    if cart_item is None:
        raise HTTPException(status_code=404, detail="Cart item not found")

    # Update cart total
    crud.calculate_cart_total(db, cart_item.cart_id)

    return cart_item


@router.delete("/cart/items/{item_id}")
def remove_cart_item(item_id: int, db: Session = Depends(get_db)):
    """
    Remove an item from cart
    """
    cart_item = crud.remove_cart_item(db, item_id=item_id)
    if cart_item is None:
        raise HTTPException(status_code=404, detail="Cart item not found")

    return {"message": "Item removed from cart"}


@router.get("/{customer_id}/call-history", response_model=List[schemas.CallHistory])
def get_customer_call_history(customer_id: int, db: Session = Depends(get_db)):
    """
    Get customer's call history
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    return crud.get_customer_call_history(db, customer_id=customer_id)


@router.get("/{customer_id}/order-history", response_model=List[schemas.Order])
def get_customer_order_history(customer_id: int, db: Session = Depends(get_db)):
    """
    Get customer's order history
    """
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")

    return crud.get_customer_order_history(db, customer_id=customer_id)


@router.post(
    "/{customer_id}/prompt-preview", response_model=schemas.PromptPreviewResponse
)
def get_prompt_preview(
    customer_id: int,
    request: schemas.PromptPreviewRequest,
    db: Session = Depends(get_db),
):
    """
    Generate a preview of the system prompt and first message for a call.
    """
    preview = crud.generate_system_prompt(
        db=db,
        customer_id=customer_id,
        custom_prompt=request.custom_prompt,
        custom_first_message=request.custom_first_message,
        webhook_data=request.webhook_data,
    )
    if not preview:
        raise HTTPException(status_code=404, detail="Customer not found")

    return schemas.PromptPreviewResponse(**preview)
