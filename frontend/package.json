{"name": "ecommerce-call-center-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "elevenlabs": "^1.59.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-modal": "^3.16.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@tailwindcss/vite": "4.0.0-alpha.3", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "4.0.0-alpha.3", "vite": "^5.0.0"}}