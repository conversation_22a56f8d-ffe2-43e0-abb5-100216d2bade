import React from 'react';
import { Link } from 'react-router-dom';
import { Edit, Trash2, Eye, Package, Tag } from 'lucide-react';
import { formatCurrency, getStatusBadgeVariant } from '../utils/helpers';

export default function ProductCard({ product, onEdit, onDelete }) {
  const stockStatus = product.stock_quantity > 10 ? 'in-stock' : 
                     product.stock_quantity > 0 ? 'low-stock' : 'out-of-stock';
  
  const stockColor = stockStatus === 'in-stock' ? 'text-green-600' :
                    stockStatus === 'low-stock' ? 'text-yellow-600' : 'text-red-600';

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      {/* Product Image */}
      <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-100">
        {product.image_url ? (
          <img
            src={product.image_url}
            alt={product.name}
            className="h-48 w-full object-cover object-center"
          />
        ) : (
          <div className="h-48 w-full flex items-center justify-center">
            <Package className="h-12 w-12 text-gray-400" />
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {product.name}
            </h3>
            {product.category && (
              <div className="flex items-center mt-1">
                <Tag className="h-3 w-3 text-gray-400 mr-1" />
                <span className="text-xs text-gray-500">{product.category}</span>
              </div>
            )}
          </div>
          <div className="ml-2 flex-shrink-0">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              product.is_active 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {product.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        {/* Description */}
        {product.description && (
          <p className="mt-2 text-sm text-gray-600 line-clamp-2">
            {product.description}
          </p>
        )}

        {/* Price and Stock */}
        <div className="mt-3 flex items-center justify-between">
          <div>
            <p className="text-lg font-bold text-gray-900">
              {formatCurrency(product.price)}
            </p>
            <p className={`text-sm ${stockColor}`}>
              Stock: {product.stock_quantity}
            </p>
          </div>
          
          {/* Sizes/Colors indicators */}
          <div className="flex flex-col items-end">
            {product.sizes && product.sizes.length > 0 && (
              <span className="text-xs text-gray-500">
                {product.sizes.length} size{product.sizes.length > 1 ? 's' : ''}
              </span>
            )}
            {product.colors && product.colors.length > 0 && (
              <span className="text-xs text-gray-500">
                {product.colors.length} color{product.colors.length > 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>

        {/* Brand and SKU */}
        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
          {product.brand && <span>Brand: {product.brand}</span>}
          {product.sku && <span>SKU: {product.sku}</span>}
        </div>

        {/* Actions */}
        <div className="mt-4 flex items-center justify-between">
          <Link
            to={`/products/${product.id}`}
            className="btn btn-outline btn-sm"
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Link>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onEdit(product)}
              className="btn btn-ghost btn-sm"
              title="Edit product"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(product.id)}
              className="btn btn-ghost btn-sm text-red-600 hover:text-red-700"
              title="Delete product"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
