# 1. Use a slim and official Python base image
FROM python:3.12-slim

# 2. Set the working directory
WORKDIR /app

# 4. Copy only the files required for dependency installation
# This leverages Docker layer caching. The next step will only re-run if these files change.
COPY pyproject.toml ./

# 5. Install build dependencies for packages like webrtcvad
RUN apt-get update && apt-get install -y build-essential

# 6. Install dependencies from pyproject.toml
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir g711 && \
    pip install --no-cache-dir .
# 7. Copy the rest of your application code
COPY . .

# Expose the port the app runs on
EXPOSE 8000

# Use uvicorn to run the FastAPI application, which is standard for production.
# The app object is in 'app/main.py'.
CMD ["python", "main.py"]
