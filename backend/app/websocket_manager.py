import asyncio
import logging
from typing import Any, <PERSON>waitable, Callable

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from .services.conversation_service import ConversationManager

logger = logging.getLogger(__name__)


class WebSocketManager:
    def __init__(self, websocket: WebSocket, db: Session):
        self.websocket = websocket
        self.db = db
        self.conversation_manager = ConversationManager(db)

    async def handle_connection(
        self,
        input_handler: Callable[[Any], Awaitable[str]],
        output_handler: Callable[[str], Awaitable[None]],
        first_message_handler: Callable[[str], Awaitable[None]],
        tool_handler: Callable[[dict], Awaitable[None]] = None,
    ):
        await self.websocket.accept()
        logger.info("WebSocket connection accepted.")

        call_history_id = None
        try:
            # The first message from the client is always the init message
            init_message = await self.websocket.receive_json()
            data = init_message.get("data", init_message)
            call_history_id = data.get("call_history_id") or data.get("callHistoryId")

            if not call_history_id:
                raise ValueError("call_history_id not found in init message")

            await self.conversation_manager.handle_conversation(
                call_history_id=call_history_id,
                input_handler=input_handler,
                output_handler=output_handler,
                first_message_handler=first_message_handler,
                get_init_message=lambda: asyncio.sleep(
                    0, result=init_message
                ),  # Wrap init_message
                tool_handler=tool_handler,
                websocket=self.websocket,
            )

        except WebSocketDisconnect:
            logger.info("Client disconnected.")
        except Exception as e:
            logger.error(f"Error in WebSocket connection: {e}", exc_info=True)
        finally:
            logger.info("Closing WebSocket connection.")
