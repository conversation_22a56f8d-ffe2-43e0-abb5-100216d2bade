# E-commerce & Call Center Backend

Modern FastAPI backend with Python 3.12+ and uv package management.

## Requirements

- **Python 3.12+** (required)
- **uv** (fast Python package manager)

## Quick Start

### Option 1: Automated Setup

```bash
# Run the setup script
python scripts/setup.py
```

### Option 2: Manual Setup

```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh  # Unix/macOS
# or
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"  # Windows

# Create virtual environment
uv venv

# Activate virtual environment
source .venv/bin/activate  # Unix/macOS
# or
.venv\Scripts\activate  # Windows

# Install dependencies
uv pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Initialize database
uv run python init_db.py

# Start server
uv run python main.py
```

## Development Commands

### Using Makefile (Recommended)

```bash
make setup      # Complete project setup
make dev        # Start development server
make install    # Install dependencies
make test       # Run tests
make lint       # Run linting
make format     # Format code
make clean      # Clean up files
```

### Using uv directly

```bash
# Install dependencies
uv pip install -r requirements.txt

# Install development dependencies
uv pip install -e .[dev]

# Run the application
uv run python main.py

# Run tests
uv run pytest

# Format code
uv run black app/
uv run isort app/

# Type checking
uv run mypy app/
```

## Project Structure

```
backend/
├── app/                    # Application code
│   ├── __init__.py
│   ├── main.py            # FastAPI app
│   ├── config.py          # Configuration
│   ├── database.py        # Database setup
│   ├── models.py          # SQLAlchemy models
│   ├── schemas.py         # Pydantic schemas
│   ├── crud.py            # Database operations
│   ├── routers/           # API routes
│   └── services/          # External services
├── scripts/               # Utility scripts
│   ├── setup.py          # Project setup
│   └── dev.py            # Development server
├── pyproject.toml        # Project configuration
├── requirements.txt      # Dependencies
├── uv.lock              # Dependency lock file
├── Makefile             # Development commands
├── Dockerfile           # Container configuration
└── .env.example         # Environment template
```

## Configuration

Copy `.env.example` to `.env` and configure:

```env
# Database
DATABASE_URL=sqlite:///./app.db

# Twilio (for voice calls)
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_phone_number

# ElevenLabs (for AI voice agents)
ELEVENLABS_API_KEY=your_api_key
ELEVENLABS_AGENT_ID=your_agent_id

# Security
SECRET_KEY=your-secret-key-here
```

## API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Features

- **FastAPI** with automatic OpenAPI documentation
- **SQLAlchemy** ORM with SQLite/PostgreSQL support
- **Pydantic** for data validation
- **Twilio** integration for voice calls
- **ElevenLabs** AI voice agents
- **Modern Python 3.12+** features
- **uv** for fast package management

## Why Python 3.12?

This project uses Python 3.12+ for:
- **Performance improvements** (10-15% faster than 3.11)
- **Better error messages** with enhanced tracebacks
- **Type system improvements** with generic type syntax
- **New f-string features** for better string formatting
- **Improved asyncio** performance and features

## Why uv?

- **10-100x faster** than pip for package installation
- **Better dependency resolution** with lockfile support
- **Drop-in replacement** for pip commands
- **Built in Rust** for maximum performance
- **Modern Python tooling** with excellent developer experience
