# Changelog

## [1.0.0] - 2024-01-XX

### 🚀 Major Changes
- **Upgraded to Python 3.12+** for better performance and modern features
- **Migrated to uv** for 10-100x faster package management
- **Fixed Pydantic Settings configuration** to handle extra environment variables

### ✅ Fixed Issues
- **Pydantic ValidationError**: Added missing fields (`node_env`, `port`, `elevenlabs_voice_id`, `base_url`) to Settings class
- **Email validation**: Added `email-validator` dependency for Pydantic EmailStr support
- **Twilio imports**: Updated to use `twilio.twiml.voice_response.VoiceResponse` for newer Twilio versions
- **Missing imports**: Added `func` and `models` imports in voice_calls router
- **Port configuration**: Made port configurable via environment variables

### 📦 Dependencies Updated
- FastAPI: `>=0.110.0` (was `>=0.104.1`)
- Uvicorn: `>=0.27.0` (was `>=0.24.0`)
- SQLAlchemy: `>=2.0.25` (was `>=2.0.23`)
- Pydantic: `>=2.6.0` with email support
- Twilio: `>=9.0.0` (was `>=8.10.3`)
- ElevenLabs: `>=1.0.0` (was `>=0.2.26`)
- Added: `email-validator>=2.1.0`

### 🔧 Configuration Changes
- **Settings class**: Now ignores extra environment variables instead of raising errors
- **Port configuration**: Server now uses `PORT` environment variable (default: 8000)
- **Environment variables**: Added support for `NODE_ENV`, `BASE_URL`, `ELEVENLABS_VOICE_ID`

### 🛠️ Development Improvements
- **Hatch build configuration**: Added proper wheel packaging support
- **Python version checking**: Scripts now verify Python 3.12+ is installed
- **Enhanced error handling**: Better error messages for missing dependencies
- **Updated documentation**: Comprehensive setup instructions for Python 3.12

### 🎯 Performance Benefits
- **Python 3.12**: 10-15% performance improvement over previous versions
- **uv package manager**: 10-100x faster dependency installation
- **Better type checking**: Enhanced type system with Python 3.12 features
- **Improved asyncio**: Better performance for web applications

### 📚 Documentation
- Added `backend/README.md` with Python 3.12 specific instructions
- Updated main README with uv installation and usage
- Enhanced Makefile with comprehensive development commands
- Added setup scripts for automated project configuration

### 🔄 Migration Notes
If upgrading from a previous version:

1. **Upgrade Python**: Ensure Python 3.12+ is installed
2. **Install uv**: `curl -LsSf https://astral.sh/uv/install.sh | sh`
3. **Update environment**: Add new variables to `.env` file
4. **Reinstall dependencies**: `uv pip install -r requirements.txt`
5. **Test configuration**: Run `uv run python init_db.py` to verify setup

### 🧪 Testing
- All endpoints tested and working
- Database initialization successful
- API documentation available at `/docs`
- Health checks passing at `/health` and `/api/status`
