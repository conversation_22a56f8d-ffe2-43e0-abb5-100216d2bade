import os
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

load_dotenv()


class Settings(BaseSettings):
    # Pydantic V2 uses model_config to configure model behavior
    model_config = SettingsConfigDict(extra="ignore")

    base_url: str = "skilled-lemur-yearly.ngrok-free.app"
    database_url: str = os.environ.get("DATABASE_URL") or "sqlite:///./app.db"

    llm_provider: str = "google"
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-4-turbo"
    gemini_api_key: Optional[str] = None
    gemini_model: str = "gemini-2.5-flash"

    stt_provider: str = "local"
    assemblyai_api_key: Optional[str] = None
    nova_api_key: Optional[str] = None

    tts_provider: str = "local"
    elevenlabs_api_key: Optional[str] = None
    elevenlabs_voice_id: Optional[str] = None
    elevenlabs_language_code: Optional[str] = None
    nova_voice_id: Optional[str] = None

    twilio_account_sid: Optional[str] = None
    twilio_auth_token: Optional[str] = None
    twilio_phone_number: Optional[str] = None
    cors_origins: list[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "ws://localhost:8000",
    ]
    port: int = 8000
    debug: bool = False
    use_langchain_agent: bool = True


settings = Settings()
