# Realtime Text-to-Speech Implementation

This document describes the realtime TTS implementation for both Nova (Deepgram) and ElevenLabs services.

## Overview

The realtime TTS implementation allows audio generation to begin immediately as text chunks become available, rather than waiting for the complete text. This significantly reduces latency in conversational AI applications.

## Key Benefits

1. **Lower Latency**: Audio playback starts as soon as the first text chunk is processed
2. **Better User Experience**: Users hear responses immediately, creating more natural conversations
3. **Streaming Integration**: Perfect for LLM streaming where text is generated incrementally
4. **Interruption Support**: Users can interrupt responses mid-stream

## Implementation Details

### Base Interface

All TTS services inherit from `TTSBase` which now includes:

```python
async def stream_realtime(
    self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
) -> AsyncGenerator[str, None]:
    """Realtime streaming audio from text chunks using WebSocket connections."""
```

### Nova (Deepgram) Implementation

The Nova implementation uses Deepgram's WebSocket API for realtime TTS:

- **WebSocket Connection**: Uses `DeepgramClient.speak.websocket.v("1")`
- **Audio Format**: Linear16 PCM at 24kHz sample rate
- **Model**: Configurable via `NOVA_VOICE_ID` (defaults to "aura-2-thalia-en")
- **Streaming**: Sends text chunks as they arrive and yields audio immediately

#### Configuration

```env
NOVA_API_KEY=your_deepgram_api_key
NOVA_VOICE_ID=aura-2-thalia-en  # Optional, defaults to aura-2-thalia-en
```

### ElevenLabs Implementation

The ElevenLabs implementation uses their WebSocket streaming API:

- **WebSocket Connection**: `wss://api.elevenlabs.io/v1/text-to-speech/{voice_id}/stream-input`
- **Model**: Uses `eleven_flash_v2_5` for optimal latency
- **Buffering**: Optimized chunk length schedule `[50, 120, 160, 250]` for low latency
- **Audio Format**: Base64 encoded audio chunks

#### Configuration

```env
ELEVENLABS_API_KEY=your_elevenlabs_api_key
ELEVENLABS_VOICE_ID=your_voice_id
ELEVENLABS_LANGUAGE_CODE=en  # Optional
```

## Usage Examples

### Basic Usage

```python
from app.tts.factory import get_tts_service
import asyncio

async def text_generator():
    chunks = ["Hello", "world", "this", "is", "streaming", "TTS"]
    for chunk in chunks:
        yield chunk + " "
        await asyncio.sleep(0.1)

tts_service = get_tts_service()
interruption_event = asyncio.Event()

async for audio_chunk in tts_service.stream_realtime(text_generator(), interruption_event):
    # Process audio chunk (send to player, save to file, etc.)
    print(f"Received audio chunk: {len(audio_chunk)} bytes")
```

### Integration with LLM Streaming

```python
async def llm_text_stream():
    # Your LLM streaming implementation
    async for text_chunk in your_llm.stream("Tell me about the weather"):
        yield text_chunk

# Start TTS as soon as LLM begins generating text
async for audio_chunk in tts_service.stream_realtime(llm_text_stream(), interruption_event):
    # Audio starts playing while LLM is still generating
    await send_audio_to_client(audio_chunk)
```

### Interruption Handling

```python
# User can interrupt at any time
if user_wants_to_interrupt:
    interruption_event.set()  # This will stop TTS generation
```

## Performance Considerations

### Latency Optimization

1. **ElevenLabs**: Uses `eleven_flash_v2_5` model and optimized chunk scheduling
2. **Nova**: Uses linear16 encoding for minimal processing overhead
3. **Buffering**: Both implementations minimize buffering for fastest response

### Error Handling

- WebSocket connection failures are handled gracefully
- API key validation occurs before streaming begins
- Interruption events are respected throughout the streaming process

### Resource Management

- WebSocket connections are properly closed after streaming
- Memory usage is optimized by yielding chunks immediately
- Background tasks are cleaned up on interruption

## Testing

### Test Scripts

1. **Basic Testing**: `backend/scripts/test_realtime_tts.py`
   - Tests both Nova and ElevenLabs realtime streaming
   - Compares performance with regular streaming

2. **Integration Example**: `backend/examples/realtime_tts_example.py`
   - Shows integration with simulated LLM streaming
   - Demonstrates interruption handling

### Running Tests

```bash
cd backend

# Test realtime TTS functionality
python scripts/test_realtime_tts.py

# Run integration example
python examples/realtime_tts_example.py
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failures**
   - Check API keys are correctly configured
   - Verify network connectivity
   - Ensure voice IDs are valid

2. **Audio Quality Issues**
   - For Nova: Try different voice models
   - For ElevenLabs: Adjust voice settings in the code

3. **Latency Issues**
   - Reduce text chunk sizes for faster streaming
   - Check network latency to API endpoints
   - Consider using different models optimized for speed

### Debug Logging

Enable debug logging to see detailed WebSocket communication:

```python
import logging
logging.getLogger("app.tts").setLevel(logging.DEBUG)
```

## Migration from Regular Streaming

Existing code using regular `stream()` method will continue to work. To use realtime streaming:

1. Replace `tts.stream()` with `tts.stream_realtime()`
2. Ensure text is provided in smaller chunks for optimal latency
3. Handle interruption events appropriately

## Future Enhancements

- Support for additional voice parameters in realtime mode
- Automatic fallback to regular streaming if WebSocket fails
- Metrics collection for latency monitoring
- Support for additional audio formats
