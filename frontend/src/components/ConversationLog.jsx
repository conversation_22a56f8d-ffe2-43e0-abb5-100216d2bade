import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';

const LogEntry = ({ log }) => {
  const [isOpen, setIsOpen] = useState(true);

  const renderContent = () => {
    switch (log.event_type) {
      case 'system_prompt':
        return <pre className="whitespace-pre-wrap text-xs">{log.data.content}</pre>;
      case 'user_message':
        return <p>{log.data.content}</p>;
      case 'agent_message':
        return <p>{log.data.content}</p>;
      case 'tool_start':
        return (
          <div>
            <p><strong>Tool:</strong> {log.data.tool}</p>
            <pre className="whitespace-pre-wrap text-xs">{log.data.input}</pre>
          </div>
        );
      case 'tool_end':
        return <pre className="whitespace-pre-wrap text-xs">{typeof log.data.output === 'object' ? JSON.stringify(log.data.output, null, 2) : log.data.output}</pre>;
      case 'llm_start':
        return <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(log.data.messages, null, 2)}</pre>;
      case 'llm_end':
        return <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(log.data.response, null, 2)}</pre>;
      default:
        return <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(log.data, null, 2)}</pre>;
    }
  };

  return (
    <div className="border-t py-2">
      <div className="flex items-center cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
        {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        <strong className="ml-2">{log.event_type}</strong>
        <span className="ml-auto text-xs text-gray-500">{new Date(log.timestamp).toLocaleTimeString()}</span>
      </div>
      {isOpen && <div className="mt-2 pl-6 bg-gray-50 p-2 rounded">{renderContent()}</div>}
    </div>
  );
};

export default function ConversationLog({ log }) {
  if (!log || log.length === 0) {
    return (
        <div className="text-center py-4">
            <p className="text-sm text-gray-500">No conversation log available.</p>
        </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Developer Log</h3>
      <div className="bg-white rounded-lg border border-gray-200 p-4 max-h-96 overflow-y-auto">
        {log.map((entry, index) => (
            <LogEntry key={index} log={entry} />
        ))}
      </div>
    </div>
  );
}
