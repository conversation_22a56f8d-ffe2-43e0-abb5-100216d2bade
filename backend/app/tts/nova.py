import logging
import base64
import asyncio
import json
import websockets
from ..config import settings
from .base import TTSBase
from typing import Async<PERSON>enerator
from deepgram import DeepgramClient, SpeakOptions, SpeakWebSocketEvents
from ..performance_tracker import track_latency

logger = logging.getLogger(__name__)


class NovaTTS(TTSBase):
    def __init__(self):
        self.client = DeepgramClient(settings.nova_api_key)
        logger.info("NovaTTS (Deepgram) service initialized.")

    @track_latency("tts", "nova_stream")
    async def stream(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        logger.info("Starting Nova (Deepgram) TTS generation...")
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            options = SpeakOptions(
                model=settings.nova_voice_id,
                encoding="linear16",
                sample_rate=24000,
            )

            source = {"text": text}

            response = self.client.speak.v("1").stream(source, options)

            audio_stream = response.stream

            chunk_size = 1024
            while True:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping TTS.")
                    break

                chunk = audio_stream.read(chunk_size)
                if not chunk:
                    break

                yield base64.b64encode(chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) TTS stream: {e}", exc_info=True)
            raise

    @track_latency("tts", "nova_stream_realtime")
    async def stream_realtime(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        """
        Realtime TTS streaming using Deepgram WebSocket API.
        Streams audio as text chunks become available.
        """
        logger.info("Starting Nova (Deepgram) realtime TTS streaming...")

        if not settings.nova_api_key:
            logger.error("Nova API key not configured")
            return

        try:
            # Create WebSocket connection to Deepgram
            dg_connection = self.client.speak.websocket.v("1")

            # Audio buffer to collect chunks
            audio_chunks = []
            connection_closed = False

            def on_open(self, open, **kwargs):
                logger.info(f"Deepgram WebSocket connection opened: {open}")

            def on_binary_data(self, data, **kwargs):
                logger.debug("Received audio chunk from Deepgram")
                audio_chunks.append(base64.b64encode(data).decode("utf-8"))

            def on_close(self, close, **kwargs):
                nonlocal connection_closed
                logger.info(f"Deepgram WebSocket connection closed: {close}")
                connection_closed = True

            def on_error(self, error, **kwargs):
                logger.error(f"Deepgram WebSocket error: {error}")

            # Set up event handlers
            dg_connection.on(SpeakWebSocketEvents.Open, on_open)
            dg_connection.on(SpeakWebSocketEvents.AudioData, on_binary_data)
            dg_connection.on(SpeakWebSocketEvents.Close, on_close)
            dg_connection.on(SpeakWebSocketEvents.Error, on_error)

            # Configure audio options for realtime streaming
            options = SpeakOptions(
                model=settings.nova_voice_id or "aura-2-thalia-en",
                encoding="linear16",
                sample_rate=24000,
            )

            # Start the connection
            if not dg_connection.start(options):
                logger.error("Failed to start Deepgram WebSocket connection")
                return

            # Process text chunks as they arrive
            async for text_chunk in text_generator:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping realtime TTS.")
                    break

                if text_chunk.strip():
                    logger.debug(f"Sending text chunk to Deepgram: {text_chunk[:50]}...")
                    dg_connection.send_text(text_chunk)

                    # Small delay to allow audio generation
                    await asyncio.sleep(0.1)

                    # Yield any available audio chunks
                    while audio_chunks:
                        yield audio_chunks.pop(0)

            # Flush any remaining text and get final audio
            dg_connection.flush()

            # Wait a bit for final audio chunks
            await asyncio.sleep(0.5)

            # Yield any remaining audio chunks
            while audio_chunks:
                yield audio_chunks.pop(0)

            # Clean up connection
            dg_connection.finish()

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) realtime TTS stream: {e}", exc_info=True)
            raise

    async def list_voices(self):
        logger.info("Listing Nova (Deepgram) voices is not supported via the SDK.")
        return []
