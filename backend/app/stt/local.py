import asyncio
import json
import logging
import os

from vosk import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Model

from .base import STTBase

logger = logging.getLogger(__name__)


class LocalSTT(STTBase):
    def __init__(self):
        # Construct an absolute path to the model directory
        model_path = os.path.join(os.path.dirname(__file__), "..", "..", "vosk-model")

        try:
            self.model = Model(model_path)
            logger.info(f"Vosk model loaded successfully from {model_path}.")
        except Exception as e:
            logger.error(f"Failed to load Vosk model from {model_path}. Error: {e}")
            raise

    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
    ):
        recognizer = Kaldi<PERSON><PERSON>ognizer(self.model, sample_rate)
        logger.info(f"Local STT started with sample rate {sample_rate}Hz.")

        try:
            while True:
                audio_chunk = await audio_queue.get()
                if audio_chunk is None:
                    break

                if recognizer.AcceptWaveform(audio_chunk):
                    result = json.loads(recognizer.Result())
                    if result.get("text"):
                        logger.info(f"User (final): {result['text']}")
                        await transcript_queue.put((result["text"], True))
                else:
                    partial_result = json.loads(recognizer.PartialResult())
                    if partial_result.get("partial"):
                        logger.info(f"User (interim): {partial_result['partial']}")
                        await transcript_queue.put((partial_result["partial"], False))

            # Process any remaining audio
            final_result = json.loads(recognizer.FinalResult())
            if final_result.get("text"):
                await transcript_queue.put((final_result["text"], True))

        except Exception as e:
            logger.error(f"Error in Local STT transcription: {e}")
        finally:
            await transcript_queue.put((None, None))
            logger.info("Local STT finished.")
