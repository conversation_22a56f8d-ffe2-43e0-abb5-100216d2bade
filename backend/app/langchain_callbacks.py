import logging
from datetime import datetime
from typing import Any, Dict, List, Union

from langchain.callbacks.base import Async<PERSON>allbackHandler
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.outputs import ChatGenerationChunk, GenerationChunk
from sqlalchemy.orm import Session

from . import crud
from .global_websocket_manager import manager as global_ws_manager
from .schemas import ConversationLogEvent

logger = logging.getLogger(__name__)


class ConversationCallbackHandler(AsyncCallbackHandler):
    def __init__(self, db: Session, call_history_id: int, websocket: Any):
        self.db = db
        self.call_history_id = call_history_id
        self.websocket = websocket

    async def _log_and_send_event(self, event_data: ConversationLogEvent):
        try:
            crud.add_conversation_log_event(self.db, self.call_history_id, event_data)

            # Broadcast to frontend clients
            await global_ws_manager.broadcast(
                self.call_history_id,
                {"type": "conversation_log", "log": event_data.model_dump(mode="json")},
            )
        except Exception as e:
            logger.error(f"Error in callback handler: {e}", exc_info=True)
            self.db.rollback()

    async def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[Union[AIMessage, HumanMessage]]],
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            event_type="llm_start",
            timestamp=datetime.now(),
            data={"messages": [msg.dict() for msg in messages[0]]},
            source="langchain",
        )
        await self._log_and_send_event(event)

    async def on_tool_start(
        self,
        serialized: Dict[str, Any],
        input_str: str,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            event_type="tool_start",
            timestamp=datetime.now(),
            data={"tool": serialized.get("name", "unknown_tool"), "input": input_str},
            source="langchain",
        )
        await self._log_and_send_event(event)

    async def on_tool_end(
        self,
        output: str,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            event_type="tool_end",
            timestamp=datetime.now(),
            data={"output": output},
            source="langchain",
        )
        await self._log_and_send_event(event)

    async def on_llm_end(
        self,
        response,
        **kwargs: Any,
    ) -> None:
        logger.info(f"on_llm_end response: {response.dict()}")
        event = ConversationLogEvent(
            event_type="llm_end",
            timestamp=datetime.now(),
            data={"response": response.dict()},
            source="langchain",
        )
        await self._log_and_send_event(event)
