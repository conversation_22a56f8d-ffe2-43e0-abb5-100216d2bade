import logging

from fastapi import APIRouter, Request, Response
from twilio.twiml.voice_response import Connect, VoiceResponse

from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/twilio", tags=["twilio"])


@router.post("/twiml/{call_history_id}")
async def generate_twiml(request: Request, call_history_id: int):
    """
    Generates TwiML for Twilio to connect to the WebSocket for ConversationRelay.
    """
    # Construct the WebSocket URL for the ConversationRelay
    ws_url = f"wss://{settings.base_url}/api/voice/phone-relay/{call_history_id}"

    response = VoiceResponse()
    connect = Connect()
    connect.conversation_relay(url=ws_url)
    response.append(connect)

    logger.info(
        f"Generated TwiML for call_history_id {call_history_id}: {str(response)}"
    )

    return Response(content=str(response), media_type="application/xml")
