import logging
from typing import Optional

from fastapi import APIRouter, Depends, Form, HTTPException, Request, Response
from sqlalchemy import func
from sqlalchemy.orm import Session

from .. import crud, models, schemas
from ..config import settings
from ..database import get_db
from ..services.twilio_service import twilio_service
from ..stt.base import STTBase
from ..tts.base import TTSBase
from ..tts.elevenlabs import ElevenLabsTTS

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/voice", tags=["voice_calls"])


# This is a placeholder for a proper service factory
def get_tts_service() -> TTSBase:
    return ElevenLabsTTS()


@router.post("/outbound-call", response_model=schemas.VoiceCallResponse)
def initiate_outbound_call(
    call_request: schemas.OutboundCallRequest,
    db: Session = Depends(get_db),
    tts_service: TTSBase = Depends(get_tts_service),
):
    """
    Initiate an outbound call to a customer
    """
    # Verify customer exists
    customer = crud.get_customer(db, customer_id=call_request.customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Get customer's cart data for context
    cart = crud.get_customer_active_cart(db, customer_id=call_request.customer_id)
    cart_data = {"items": [], "total": 0, "cart_id": None}

    if cart:
        cart_data = {
            "items": [
                {
                    "id": item.id,
                    "product_name": item.product.name,
                    "quantity": item.quantity,
                    "size": item.size,
                    "color": item.color,
                    "price": item.price,
                }
                for item in cart.items
            ],
            "total": cart.total_amount,
            "cart_id": cart.id,
        }

    # Create call history record
    call_history = crud.create_call_history(
        db,
        schemas.CallHistoryCreate(
            customer_id=call_request.customer_id,
            phone_number=call_request.phone_number,
            call_metadata={
                "platform": "phone",
                "custom_prompt": call_request.custom_prompt,
                "custom_first_message": call_request.custom_first_message,
                "webhook_data": call_request.webhook_data,
                "cart_data": cart_data,
            },
        ),
    )

    # Create ElevenLabs conversation context
    customer_data = {
        "id": customer.id,
        "name": customer.name,
        "phone": customer.phone,
        "email": customer.email,
        "enrichment": customer.enrichment,
    }

    if not isinstance(tts_service, ElevenLabsTTS):
        raise HTTPException(status_code=500, detail="TTS service is not an instance of ElevenLabs")

    # Initiate Twilio call, which will connect to our WebSocket
    call_response = twilio_service.make_outbound_call(
        to_number=call_request.phone_number, call_history_id=call_history.id
    )

    if not call_response["success"]:
        raise HTTPException(status_code=500, detail=f"Failed to initiate call: {call_response['error']}")

    # Update call history with Twilio call SID and prompts
    crud.update_call_history(
        db,
        call_history.id,
        call_sid=call_response["call_sid"],
        status="initiated",
        system_prompt=call_request.custom_prompt,
        first_message=call_request.custom_first_message,
    )

    return schemas.VoiceCallResponse(
        success=True,
        message="Call initiated successfully",
        call_sid=call_response["call_sid"],
        call_history_id=call_history.id,
    )


@router.post("/outbound-web-call", response_model=schemas.WebCallResponse)
def initiate_outbound_web_call(call_request: schemas.OutboundWebCallRequest, db: Session = Depends(get_db)):
    """
    Initiate an outbound web call to a customer
    """
    # Verify customer exists
    customer = crud.get_customer(db, customer_id=call_request.customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Create call history record
    call_history = crud.create_call_history(
        db,
        schemas.CallHistoryCreate(
            customer_id=call_request.customer_id,
            call_type="web",
            call_metadata={"platform": "web"},
        ),
    )

    crud.update_call_history(
        db,
        call_history.id,
        status="initiated",
        system_prompt=call_request.custom_prompt,
        first_message=call_request.custom_first_message,
    )

    return schemas.WebCallResponse(
        success=True,
        message="Web call initiated successfully",
        call_history_id=call_history.id,
    )


@router.post("/outbound-text-chat", response_model=schemas.WebCallResponse)
def initiate_outbound_text_chat(call_request: schemas.OutboundWebCallRequest, db: Session = Depends(get_db)):
    """
    Initiate an outbound text chat with a customer
    """
    # Verify customer exists
    customer = crud.get_customer(db, customer_id=call_request.customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Create call history record
    call_history = crud.create_call_history(
        db,
        schemas.CallHistoryCreate(
            customer_id=call_request.customer_id,
            call_type="text",
            call_metadata={"platform": "text"},
        ),
    )

    crud.update_call_history(
        db,
        call_history.id,
        status="initiated",
        system_prompt=call_request.custom_prompt,
        first_message=call_request.custom_first_message,
    )

    return schemas.WebCallResponse(
        success=True,
        message="Text chat initiated successfully",
        call_history_id=call_history.id,
    )


@router.post("/webhook/{call_history_id}")
async def twilio_webhook(call_history_id: int, request: Request, db: Session = Depends(get_db)):
    """
    Handle Twilio webhook for voice interactions
    """
    # Get call history
    call_history = crud.get_call_history(db, call_id=call_history_id)
    if not call_history:
        raise HTTPException(status_code=404, detail="Call history not found")

    # Parse Twilio request
    form_data = await request.form()
    call_status = form_data.get("CallStatus")
    call_sid = form_data.get("CallSid")

    logger.info(f"Twilio webhook: Call {call_sid} status {call_status}")

    # Update call status
    crud.update_call_history(db, call_history_id, status=call_status, call_sid=call_sid)

    if call_status == "answered":
        # Call was answered, start ElevenLabs conversation
        customer = crud.get_customer(db, customer_id=call_history.customer_id)

        # Create personalized greeting
        greeting = f"Hi {customer.name}! This is Sarah from our customer service team. "

        if call_history.call_metadata and call_history.call_metadata.get("custom_first_message"):
            greeting = call_history.call_metadata["custom_first_message"]
        else:
            cart_data = call_history.call_metadata.get("cart_data", {})
            if cart_data.get("items"):
                greeting += f"I noticed you have {len(cart_data['items'])} item(s) in your cart. I'm here to help you complete your purchase or answer any questions you might have."
            else:
                greeting += (
                    "I'm here to help you with any questions about our products or assist you with making a purchase."
                )

        # Generate TwiML response
        twiml_response = twilio_service.generate_twiml_response(message=greeting, next_action="gather")

        return Response(content=twiml_response, media_type="text/xml")

    elif call_status in ["completed", "failed", "busy", "no-answer"]:
        # Call ended, update final status
        crud.update_call_history(
            db,
            call_history_id,
            status=call_status,
            completed_at=func.now(),
            call_duration=form_data.get("CallDuration"),
        )

    return Response(
        content=twilio_service.generate_twiml_response("Thank you for calling!"),
        media_type="text/xml",
    )


@router.post("/webhook/{call_history_id}/status")
async def twilio_status_webhook(call_history_id: int, request: Request, db: Session = Depends(get_db)):
    """
    Handle Twilio call status updates.
    """
    form_data = await request.form()
    call_sid = form_data.get("CallSid")
    call_status = form_data.get("CallStatus")

    logger.info(f"Twilio status webhook for call {call_sid} (history ID {call_history_id}): {call_status}")

    crud.update_call_history(db, call_history_id, status=call_status, call_sid=call_sid)

    return {"status": "ok"}


@router.post("/webhook/{call_history_id}/transcript")
async def twilio_transcript_webhook(call_history_id: int, request: Request, db: Session = Depends(get_db)):
    """
    Handle Twilio transcription updates.
    """
    form_data = await request.form()
    call_sid = form_data.get("CallSid")
    transcript = form_data.get("TranscriptionText")

    logger.info(f"Twilio transcript for call {call_sid} (history ID {call_history_id}): {transcript}")

    # Append the transcript to the call history
    call_history = crud.get_call_history(db, call_id=call_history_id)
    if call_history:
        if not call_history.transcript:
            call_history.transcript = []
        call_history.transcript.append({"speaker": "user", "text": transcript})
        crud.update_call_history(db, call_history_id, transcript=call_history.transcript)

    return {"status": "ok"}


@router.get("/call-status/{call_sid}")
def get_call_status(call_sid: str):
    """
    Get the current status of a call
    """
    return twilio_service.get_call_status(call_sid)


@router.post("/end-call/{call_sid}")
def end_call(call_sid: str, db: Session = Depends(get_db)):
    """
    End an active call
    """
    result = twilio_service.end_call(call_sid)

    if result["success"]:
        # Update call history
        call_history = db.query(models.CallHistory).filter(models.CallHistory.call_sid == call_sid).first()

        if call_history:
            crud.update_call_history(db, call_history.id, status="completed", completed_at=func.now())

    return result


@router.get("/call-history/{call_id}", response_model=schemas.CallHistory)
def get_call_details(call_id: int, db: Session = Depends(get_db)):
    """
    Get detailed information about a specific call
    """
    call_history = crud.get_call_history(db, call_id=call_id)
    if not call_history:
        raise HTTPException(status_code=404, detail="Call not found")

    return call_history


@router.post("/call-history/{call_id}", response_model=schemas.CallHistory)
def update_call_details(call_id: int, details: schemas.CallHistoryUpdate, db: Session = Depends(get_db)):
    """
    Update a call with the full transcript and other details
    """
    logger.info(f"Updating call history for call_id: {call_id} with details: {details.model_dump_json()}")

    # Get the existing call history
    db_call = crud.get_call_history(db, call_id=call_id)
    if not db_call:
        raise HTTPException(status_code=404, detail="Call not found")

    # Update other fields if necessary
    if details.conversation_log:
        db_call.conversation_log = details.conversation_log
    if details.status:
        db_call.status = details.status
    if details.call_duration:
        db_call.call_duration = details.call_duration

    # Commit the changes
    db.commit()
    db.refresh(db_call)

    return db_call
