import os
import pytest
from dotenv import load_dotenv
from elevenlabs.client import ElevenLabs
from elevenlabs.core.api_error import ApiError
import re

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# --- Test Configuration ---
API_KEY = os.getenv("ELEVENLABS_API_KEY")
TEXT_TO_SPEAK = "Hello, this is a test of the ElevenLabs Text to Speech audio generation."
MODEL_ID = "eleven_turbo_v2_5"
VOICE_ID = "21m00Tcm4TlvDq8ikWAM"
STT_MODEL_ID = "scribe_v1"

# --- Pytest Test Case ---

# Skip test if API key is not available
@pytest.mark.skipif(not API_KEY, reason="ELEVENLABS_API_KEY not found in .env file")
def test_tts_to_stt_e2e():
    """
    Performs an end-to-end test of ElevenLabs TTS and STT functionality.
    1. Generates audio from text.
    2. Transcribes the generated audio back to text.
    3. Asserts that the transcription matches the original text.
    """
    print("Initializing ElevenLabs client...")
    try:
        client = ElevenLabs(api_key=API_KEY)

        # 1. Generate audio from text (TTS)
        print(f"Generating audio for the text: '{TEXT_TO_SPEAK}'")
        audio_stream = client.text_to_speech.stream(
            text=TEXT_TO_SPEAK,
            voice_id=VOICE_ID,
            model_id=MODEL_ID,
        )
        # The stream returns chunks, so we concatenate them
        audio_bytes = b"".join(chunk for chunk in audio_stream)
        assert len(audio_bytes) > 0, "TTS returned empty audio data."

        # 2. Transcribe the generated audio (STT)
        print("Transcribing generated audio...")
        response = client.speech_to_text.convert(
            file=audio_bytes,
            model_id=STT_MODEL_ID
        )
        transcribed_text = response.text

        # 3. Verify the transcription
        print(f"Expected:    '{TEXT_TO_SPEAK}'")
        print(f"Transcribed: '{transcribed_text}'")

        # Clean strings for comparison
        clean_expected = re.sub(r'[^\w\s]', '', TEXT_TO_SPEAK).lower()
        clean_transcribed = re.sub(r'[^\w\s]', '', transcribed_text).lower()
        
        clean_expected = re.sub(r'\s+', '', clean_expected)
        clean_transcribed = re.sub(r'\s+', '', clean_transcribed)

        assert clean_expected in clean_transcribed
        print("\nTest PASSED: Transcription is correct.")

    except ApiError as e:
        pytest.fail(f"ElevenLabs API error during test: {e}")
    except Exception as e:
        pytest.fail(f"An unexpected error occurred during the test: {e}")