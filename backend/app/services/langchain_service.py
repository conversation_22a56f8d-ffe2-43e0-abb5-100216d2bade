import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_core.messages import (
    AIMessage,
    AIMessageChunk,
    HumanMessage,
    SystemMessage,
)
from langchain_core.prompts import Chat<PERSON>rom<PERSON>Template, MessagesPlaceholder
from langchain_google_genai import ChatGoogleGenerativeAI
from sqlalchemy.orm import Session

from ..config import settings
from ..langchain_callbacks import ConversationCallbackHandler
from ..llms import get_llm
from ..tools import langchain_tools

logger = logging.getLogger(__name__)


class LangchainAgentService:
    def __init__(self):
        # This is now only used for the agent, not direct calls
        self.llm_for_agent = ChatGoogleGenerativeAI(
            model=settings.gemini_model,
            google_api_key=settings.gemini_api_key,
            streaming=True,
        )
        self.tools = [
            langchain_tools.get_cart_summary,
            langchain_tools.search_products,
            langchain_tools.add_cart_item_webhook,
            langchain_tools.remove_cart_item,
            langchain_tools.checkout_cart,
            langchain_tools.get_customer_by_id,
            langchain_tools.get_product_categories,
            langchain_tools.get_search_suggestions,
        ]
        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are a helpful assistant. Here is some context about the user and the call.
Use this context to inform your responses, but do not mention it unless directly asked.

Customer Information:
{customer_info}

Shopping Cart:
{cart_info}

Call Context:
{call_context}
""",
                ),
                MessagesPlaceholder(variable_name="chat_history"),
                ("user", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ]
        )
        self.agent = create_tool_calling_agent(
            self.llm_for_agent, self.tools, self.prompt
        )
        self.agent_executor = AgentExecutor(
            agent=self.agent, tools=self.tools, verbose=True, name="ecom"
        )
        self.direct_llm = get_llm()

    def _prepare_run(
        self, messages: List[Dict[str, str]], context: Optional[Dict] = None
    ):
        if not messages:
            return None, None, None, None

        input_text = messages[-1]["content"]
        chat_history = []

        system_prompt = ""
        if messages[0]["role"] == "system":
            system_prompt = messages[0]["content"]
            chat_history.append(SystemMessage(content=system_prompt))

        for msg in messages[1:-1]:
            if msg["role"] == "user":
                chat_history.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                chat_history.append(AIMessage(content=msg["content"]))

        context = context or {}
        formatted_context = {
            "customer_info": json.dumps(context.get("customer_info"), indent=2),
            "cart_info": json.dumps(context.get("cart_info"), indent=2),
            "call_context": json.dumps(context.get("call_context"), indent=2),
        }

        if system_prompt:
            # We need to merge the system prompt with the context for the direct call
            system_prompt = self.prompt.format(
                chat_history=[],
                input=input_text,
                agent_scratchpad=[],
                **formatted_context,
            )

        logger.info(f"Invoking LLM with input: {input_text}")
        return input_text, chat_history, formatted_context, system_prompt

    async def run_agent_async(
        self,
        messages: List[Dict[str, str]],
        db: Session,
        context: Optional[Dict] = None,
        call_history_id: Optional[int] = None,
        websocket: Optional[Any] = None,
    ) -> AsyncGenerator[Dict, None]:
        input_text, chat_history, formatted_context, _ = self._prepare_run(
            messages, context
        )
        if input_text is None:
            yield {}
            return

        for tool in self.tools:
            if hasattr(tool, "db"):
                tool.db = db

        config = {}
        if websocket and call_history_id:
            callback_handler = ConversationCallbackHandler(
                db=db, call_history_id=call_history_id, websocket=websocket
            )
            config["callbacks"] = [callback_handler]

        try:
            async for chunk in self.agent_executor.astream(
                {
                    "input": input_text,
                    "chat_history": chat_history,
                    **formatted_context,
                },
                config=config,
            ):
                logger.debug(f"Agent chunk: {chunk}")
                if "actions" in chunk:
                    for action in chunk["actions"]:
                        yield {
                            "tool_call": {
                                "name": action.tool,
                                "args": action.tool_input,
                            }
                        }
                elif "messages" in chunk:
                    for message in chunk["messages"]:
                        if isinstance(message, AIMessageChunk) and message.content:
                            yield {"stream_chunk": message.content}
                elif "output" in chunk:
                    yield {"output": chunk["output"]}
        except Exception as e:
            logger.error(f"Error during agent execution: {e}", exc_info=True)
            yield {"output": "I'm sorry, I encountered an error. Please try again."}

    async def run_llm_async(
        self, messages: List[Dict[str, str]], context: Optional[Dict] = None
    ) -> AsyncGenerator[Dict, None]:
        _, _, _, system_prompt_str = self._prepare_run(messages, context)

        # The first message is the generic system prompt, replace it with the fully formatted one.
        final_messages = [{"role": "system", "content": system_prompt_str}] + messages[
            1:
        ]

        logger.info("Invoking LLM directly via LLM factory.")
        try:
            async for chunk in self.direct_llm.astream(final_messages):
                yield {"stream_chunk": chunk.content}
        except Exception as e:
            logger.error(f"Error during direct LLM execution: {e}", exc_info=True)
            yield {"output": "I'm sorry, I encountered an error."}
