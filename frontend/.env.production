# This is the API URL for your production environment.
# IMPORTANT: This value is a placeholder.
# It will be overridden by the environment variable you set in your hosting provider (e.g., GCP Cloud Run).
# For example, in your gcloud deploy command, you will set:
# --set-env-vars="VITE_API_URL=https://your-production-backend-url.run.app/api"
VITE_API_URL=https://voice-backend-940496280811.europe-west1.run.app/api
