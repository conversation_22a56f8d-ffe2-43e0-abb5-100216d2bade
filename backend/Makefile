# Makefile for E-commerce & Call Center Backend

.PHONY: help install dev test lint format clean setup

# Default target
help:
	@echo "Available commands:"
	@echo "  setup     - Set up the project (install uv, create venv, install deps)"
	@echo "  install   - Install dependencies with uv"
	@echo "  dev       - Run development server"
	@echo "  test      - Run tests"
	@echo "  lint      - Run linting (flake8, mypy)"
	@echo "  format    - Format code (black, isort)"
	@echo "  clean     - Clean up generated files"
	@echo "  init-db   - Initialize database with sample data"

# Check if uv is installed
check-uv:
	@which uv > /dev/null || (echo "uv is not installed. Run 'make setup' first." && exit 1)

# Set up the project
setup:
	@echo "🚀 Setting up the project..."
	@if ! which uv > /dev/null; then \
		echo "📦 Installing uv..."; \
		curl -LsSf https://astral.sh/uv/install.sh | sh; \
	fi
	@echo "📦 Creating virtual environment..."
	uv venv
	@echo "📥 Installing dependencies..."
	uv pip install -r requirements.txt
	@echo "⚙️ Setting up environment..."
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@echo "🗄️ Initializing database..."
	uv run python init_db.py
	@echo "✅ Setup complete!"

# Install dependencies
install: check-uv
	@echo "📥 Installing dependencies..."
	uv pip install -r requirements.txt

# Install development dependencies
install-dev: check-uv
	@echo "📥 Installing development dependencies..."
	uv pip install -e .[dev]

# Run development server
dev: check-uv
	@echo "🚀 Starting development server..."
	uv run python main.py

# Initialize database
init-db: check-uv
	@echo "🗄️ Initializing database..."
	uv run python init_db.py

# Run tests
test: check-uv
	@echo "🧪 Running tests..."
	uv run python -m pytest

# Run tests with coverage
test-cov: check-uv
	@echo "🧪 Running tests with coverage..."
	uv run pytest --cov=app --cov-report=html --cov-report=term

# Run linting
lint: check-uv
	@echo "🔍 Running linting..."
	uv run flake8 app/
	uv run mypy app/

# Format code
format: check-uv
	@echo "🎨 Formatting code..."
	uv run black app/
	uv run isort app/

# Clean up
clean:
	@echo "🧹 Cleaning up..."
	rm -rf .venv/
	rm -rf __pycache__/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	rm -rf *.egg-info/
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete

# Show project status
status: check-uv
	@echo "📊 Project Status:"
	@echo "Python version: $(shell python --version)"
	@echo "uv version: $(shell uv --version)"
	@echo "Virtual environment: $(shell if [ -d .venv ]; then echo "✅ Active"; else echo "❌ Not found"; fi)"
	@echo "Dependencies: $(shell if [ -f .venv/pyvenv.cfg ]; then echo "✅ Installed"; else echo "❌ Not installed"; fi)"
	@echo "Database: $(shell if [ -f app.db ]; then echo "✅ Initialized"; else echo "❌ Not initialized"; fi)"
	@echo "Environment: $(shell if [ -f .env ]; then echo "✅ Configured"; else echo "❌ Not configured"; fi)"

# Update dependencies
update: check-uv
	@echo "📦 Updating dependencies..."
	uv pip install --upgrade -r requirements.txt

# Generate requirements.txt from pyproject.toml
freeze: check-uv
	@echo "🔒 Generating requirements.txt..."
	uv pip freeze > requirements.txt

# Run security check
security: check-uv
	@echo "🔒 Running security check..."
	uv run safety check

# Run all checks (lint, test, security)
check: lint test security
	@echo "✅ All checks passed!"
