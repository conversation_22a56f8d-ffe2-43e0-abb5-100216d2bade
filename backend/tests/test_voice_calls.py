from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

def test_initiate_outbound_web_call(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "WebCall User", "phone": "7778889999", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]

    response = client.post(
        "/api/voice/outbound-web-call",
        json={"customer_id": customer_id, "custom_prompt": "Test prompt", "custom_first_message": "Test message"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Web call initiated successfully" in data["message"]
    assert "call_history_id" in data

def test_get_call_details(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "WebCall User", "phone": "7778889999", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    call_response = client.post(
        "/api/voice/outbound-web-call",
        json={"customer_id": customer_id, "custom_prompt": "Test prompt", "custom_first_message": "Test message"},
    )
    call_history_id = call_response.json()["call_history_id"]

    response = client.get(f"/api/voice/call-history/{call_history_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == call_history_id
    assert data["customer_id"] == customer_id

def test_update_call_details(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "WebCall User", "phone": "7778889999", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    call_response = client.post(
        "/api/voice/outbound-web-call",
        json={"customer_id": customer_id, "custom_prompt": "Test prompt", "custom_first_message": "Test message"},
    )
    call_history_id = call_response.json()["call_history_id"]

    response = client.post(
        f"/api/voice/call-history/{call_history_id}",
        json={"status": "completed", "conversation_log": [{"event_type": "user_message", "data": {"content": "Hello"}, "source": "user"}], "call_duration": 60},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    assert len(data["conversation_log"]) == 1
    assert data["conversation_log"][0]["data"]["content"] == "Hello"
