import datetime

from sqlalchemy.orm import class_mapper


def model_to_dict(obj, visited=None):
    """
    Converts a SQLAlchemy model instance to a dictionary, including relationships,
    excluding internal SQLAlchemy state.
    Handles datetime objects by converting them to ISO format strings.
    """
    if visited is None:
        visited = set()

    if obj is None:
        return None

    if id(obj) in visited:
        return None  # or some placeholder for circular reference
    visited.add(id(obj))

    if not hasattr(obj, "__table__"):
        # Not a SQLAlchemy model, return as is
        if isinstance(obj, list):
            return [model_to_dict(item, visited) for item in obj]
        return obj

    data = {}
    mapper = class_mapper(obj.__class__)

    # Get columns
    for c in mapper.columns:
        value = getattr(obj, c.name)
        if isinstance(value, datetime.datetime):
            data[c.name] = value.isoformat()
        else:
            data[c.name] = value

    # Get relationships
    for r in mapper.relationships:
        value = getattr(obj, r.key)
        if value is not None:
            if isinstance(value, list):
                data[r.key] = [model_to_dict(item, visited.copy()) for item in value]
            else:
                data[r.key] = model_to_dict(value, visited.copy())
        else:
            data[r.key] = None

    return data
