#!/usr/bin/env python3
"""
Test script to verify audio format from ElevenLabs TTS.
This helps debug audio playback issues.
"""

import asyncio
import base64
import logging
import os
import sys
from typing import AsyncGenerator

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import settings
from app.tts.elevenlabs import ElevenLabsTTS

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def simple_text_generator() -> AsyncGenerator[str, None]:
    """Simple text generator for testing."""
    yield "Hello "
    await asyncio.sleep(0.1)
    yield "world "
    await asyncio.sleep(0.1)
    yield "this is a test."


def validate_base64_audio(audio_chunk: str) -> bool:
    """Validate that the audio chunk is properly base64 encoded."""
    try:
        # Try to decode the base64
        decoded = base64.b64decode(audio_chunk)
        logger.info(f"Audio chunk decoded successfully. Length: {len(decoded)} bytes")
        
        # Check if it looks like PCM audio (should have reasonable size)
        if len(decoded) < 10:
            logger.warning(f"Audio chunk seems too small: {len(decoded)} bytes")
            return False
            
        if len(decoded) > 1000000:  # 1MB seems too large for a chunk
            logger.warning(f"Audio chunk seems too large: {len(decoded)} bytes")
            return False
            
        # For PCM audio, check if the data looks reasonable
        # PCM data should have some variation, not all zeros
        if all(b == 0 for b in decoded[:100]):  # Check first 100 bytes
            logger.warning("Audio chunk appears to be all zeros")
            return False
            
        logger.info("Audio chunk validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to decode base64 audio: {e}")
        return False


async def test_elevenlabs_audio_format():
    """Test ElevenLabs audio format and encoding."""
    logger.info("Testing ElevenLabs audio format...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.error("ElevenLabs API key or voice ID not configured")
        return False
    
    try:
        tts = ElevenLabsTTS()
        interruption_event = asyncio.Event()
        
        # Test regular streaming first
        logger.info("Testing regular streaming...")
        text_gen = simple_text_generator()
        chunk_count = 0
        
        async for audio_chunk in tts.stream(text_gen, interruption_event):
            chunk_count += 1
            logger.info(f"Regular stream - chunk {chunk_count}")
            
            if not validate_base64_audio(audio_chunk):
                logger.error(f"Regular streaming - Invalid audio chunk {chunk_count}")
                return False
                
            if chunk_count >= 3:  # Test first few chunks
                break
        
        logger.info(f"Regular streaming test passed with {chunk_count} chunks")
        
        # Reset interruption event
        interruption_event.clear()
        
        # Test realtime streaming
        logger.info("Testing realtime streaming...")
        text_gen = simple_text_generator()
        chunk_count = 0
        
        async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
            chunk_count += 1
            logger.info(f"Realtime stream - chunk {chunk_count}")
            
            if not validate_base64_audio(audio_chunk):
                logger.error(f"Realtime streaming - Invalid audio chunk {chunk_count}")
                return False
                
            if chunk_count >= 3:  # Test first few chunks
                break
        
        logger.info(f"Realtime streaming test passed with {chunk_count} chunks")
        return True
        
    except Exception as e:
        logger.error(f"Error testing ElevenLabs audio format: {e}", exc_info=True)
        return False


async def test_audio_chunk_size():
    """Test if audio chunks are reasonable sizes."""
    logger.info("Testing audio chunk sizes...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.error("ElevenLabs API key or voice ID not configured")
        return
    
    try:
        tts = ElevenLabsTTS()
        interruption_event = asyncio.Event()
        
        # Test with longer text to get multiple chunks
        async def longer_text_gen():
            text = "This is a longer test to generate multiple audio chunks and analyze their sizes and timing."
            words = text.split()
            for word in words:
                yield word + " "
                await asyncio.sleep(0.1)
        
        chunk_sizes = []
        chunk_count = 0
        
        async for audio_chunk in tts.stream_realtime(longer_text_gen(), interruption_event):
            chunk_count += 1
            decoded = base64.b64decode(audio_chunk)
            chunk_sizes.append(len(decoded))
            logger.info(f"Chunk {chunk_count}: {len(decoded)} bytes")
            
            if chunk_count >= 10:  # Analyze first 10 chunks
                break
        
        if chunk_sizes:
            avg_size = sum(chunk_sizes) / len(chunk_sizes)
            min_size = min(chunk_sizes)
            max_size = max(chunk_sizes)
            
            logger.info(f"Chunk size analysis:")
            logger.info(f"  Average: {avg_size:.1f} bytes")
            logger.info(f"  Min: {min_size} bytes")
            logger.info(f"  Max: {max_size} bytes")
            logger.info(f"  Total chunks: {len(chunk_sizes)}")
            
            # Check for reasonable sizes (PCM 24kHz, 16-bit should be ~48KB per second)
            # For small chunks, expect 1-10KB range
            if min_size < 100:
                logger.warning("Some chunks are very small, might cause audio issues")
            if max_size > 50000:
                logger.warning("Some chunks are very large, might cause buffering issues")
        
    except Exception as e:
        logger.error(f"Error testing audio chunk sizes: {e}", exc_info=True)


async def main():
    """Main test function."""
    logger.info("Starting ElevenLabs audio format tests...")
    
    # Test 1: Basic audio format validation
    success = await test_elevenlabs_audio_format()
    if not success:
        logger.error("Audio format test failed!")
        return
    
    # Test 2: Chunk size analysis
    await test_audio_chunk_size()
    
    logger.info("All audio format tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
