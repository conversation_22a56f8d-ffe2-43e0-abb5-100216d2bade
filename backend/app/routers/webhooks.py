import logging

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import or_
from sqlalchemy.orm import Session

from .. import crud, models, schemas
from ..database import get_db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/get-cart-summary", response_model=schemas.CartSummaryResponse)
def get_cart_summary(
    request: schemas.CartSummaryRequest, db: Session = Depends(get_db)
):
    """
    Get cart summary for voice agent
    """
    customer = crud.get_customer(db, customer_id=request.customer_id)
    if not customer:
        return schemas.CartSummaryResponse(
            success=False, cart_summary="Customer not found.", items=[], total=0.0
        )

    cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
    if not cart or not cart.items:
        return schemas.CartSummaryResponse(
            success=True,
            cart_summary="Your cart is currently empty.",
            items=[],
            total=0.0,
        )

    # Build cart summary
    items_data = []
    total = 0.0

    for item in cart.items:
        item_data = {
            "id": item.id,
            "product_name": item.product.name,
            "quantity": item.quantity,
            "size": item.size,
            "color": item.color,
            "price": item.price,
            "subtotal": item.price * item.quantity,
        }
        items_data.append(item_data)
        total += item.price * item.quantity

    # Create summary text
    item_descriptions = []
    for item in cart.items:
        desc = f"{item.quantity}x {item.product.name}"
        if item.size:
            desc += f" (Size: {item.size})"
        if item.color:
            desc += f" (Color: {item.color})"
        desc += f" - ${item.price * item.quantity:.2f}"
        item_descriptions.append(desc)

    summary = f"You have {len(cart.items)} item{'s' if len(cart.items) > 1 else ''} in your cart: {', '.join(item_descriptions)}. Total: ${total:.2f}"

    return schemas.CartSummaryResponse(
        success=True, cart_summary=summary, items=items_data, total=total
    )


@router.post("/search-products", response_model=schemas.ProductSearchResponse)
def search_products(
    request: schemas.ProductSearchRequest, db: Session = Depends(get_db)
):
    """
    Search products for voice agent
    """
    if not request.query and not request.category:
        return schemas.ProductSearchResponse(
            success=False,
            search_results="Please provide a search query or category.",
            products=[],
        )

    products = crud.get_products(
        db, category=request.category, search=request.query, limit=10
    )

    if not products:
        search_term = request.query or request.category
        return schemas.ProductSearchResponse(
            success=True,
            search_results=f"No products found matching '{search_term}'.",
            products=[],
        )

    # Build product data
    products_data = []
    for product in products:
        product_data = {
            "id": product.id,
            "name": product.name,
            "description": product.description,
            "price": product.price,
            "category": product.category,
            "brand": product.brand,
            "sizes": product.sizes or [],
            "colors": product.colors or [],
            "stock_quantity": product.stock_quantity,
        }
        products_data.append(product_data)

    # Create search results text
    product_names = [f"{p.name} (${p.price})" for p in products]
    search_results = f"Found {len(products)} product{'s' if len(products) > 1 else ''}: {', '.join(product_names)}"

    return schemas.ProductSearchResponse(
        success=True, search_results=search_results, products=products_data
    )


@router.post("/add-cart-item", response_model=schemas.WebhookResponse)
def add_cart_item(request: schemas.AddCartItemRequest, db: Session = Depends(get_db)):
    """
    Add item to cart via voice command
    """
    customer = crud.get_customer(db, customer_id=request.customer_id)
    if not customer:
        return schemas.WebhookResponse(success=False, message="Customer not found.")

    # Find product by name (fuzzy search)
    product = (
        db.query(models.Product)
        .filter(
            models.Product.name.ilike(f"%{request.product_name}%"),
            models.Product.is_active == True,
        )
        .first()
    )

    if not product:
        return schemas.WebhookResponse(
            success=False,
            message=f"Sorry, I couldn't find a product matching '{request.product_name}'. Let me search our catalog for you.",
        )

    # Get or create active cart
    cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
    if not cart:
        cart = crud.create_cart(db, schemas.CartCreate(customer_id=request.customer_id))

    # Add item to cart
    cart_item_data = schemas.CartItemCreate(
        product_id=product.id,
        quantity=request.quantity,
        size=request.size,
        color=request.color,
    )

    cart_item = crud.add_cart_item(db, cart_id=cart.id, item=cart_item_data)
    if not cart_item:
        return schemas.WebhookResponse(
            success=False, message="Failed to add item to cart."
        )

    # Update cart total
    crud.calculate_cart_total(db, cart.id)

    # Create response message
    size_text = f" (Size: {request.size})" if request.size else ""
    color_text = f" (Color: {request.color})" if request.color else ""
    total_cost = product.price * request.quantity

    message = f"Added {request.quantity}x {product.name}{size_text}{color_text} to your cart for ${total_cost:.2f}."

    return schemas.WebhookResponse(success=True, message=message)


@router.post("/remove-cart-item", response_model=schemas.WebhookResponse)
def remove_cart_item(
    request: schemas.RemoveCartItemRequest, db: Session = Depends(get_db)
):
    """
    Remove item from cart via voice command
    """
    customer = crud.get_customer(db, customer_id=request.customer_id)
    if not customer:
        return schemas.WebhookResponse(success=False, message="Customer not found.")

    # Find product by name
    product = (
        db.query(models.Product)
        .filter(
            models.Product.name.ilike(f"%{request.product_name}%"),
            models.Product.is_active == True,
        )
        .first()
    )

    if not product:
        return schemas.WebhookResponse(
            success=False,
            message=f"Sorry, I couldn't find a product matching '{request.product_name}' in your cart.",
        )

    # Get active cart
    cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
    if not cart:
        return schemas.WebhookResponse(
            success=False, message="Your cart is empty, so I can't remove any items."
        )

    # Find cart item
    cart_item = (
        db.query(models.CartItem)
        .filter(
            models.CartItem.cart_id == cart.id, models.CartItem.product_id == product.id
        )
        .first()
    )

    if not cart_item:
        return schemas.WebhookResponse(
            success=False, message=f"{product.name} is not in your cart."
        )

    if cart_item.quantity <= request.quantity:
        # Remove item completely
        crud.remove_cart_item(db, item_id=cart_item.id)
        message = f"Removed {product.name} from your cart."
    else:
        # Reduce quantity
        update_data = schemas.CartItemUpdate(
            quantity=cart_item.quantity - request.quantity
        )
        crud.update_cart_item(db, item_id=cart_item.id, item=update_data)
        remaining = cart_item.quantity - request.quantity
        message = f"Reduced {product.name} quantity by {request.quantity}. You now have {remaining} left."

    # Update cart total
    crud.calculate_cart_total(db, cart.id)

    return schemas.WebhookResponse(success=True, message=message)


@router.post("/checkout-cart", response_model=schemas.WebhookResponse)
def checkout_cart(request: schemas.CheckoutCartRequest, db: Session = Depends(get_db)):
    """
    Process cart checkout via voice command
    """
    customer = crud.get_customer(db, customer_id=request.customer_id)
    if not customer:
        return schemas.WebhookResponse(success=False, message="Customer not found.")

    # Get active cart
    cart = crud.get_customer_active_cart(db, customer_id=request.customer_id)
    if not cart or not cart.items:
        return schemas.WebhookResponse(
            success=False,
            message="Your cart is empty. Add some items before checking out.",
        )

    # Calculate total
    total = sum(item.price * item.quantity for item in cart.items)
    item_count = len(cart.items)

    # Mark cart as completed
    cart.status = "completed"
    db.commit()

    message = f"Great! Your order has been processed. Total: ${total:.2f} for {item_count} item{'s' if item_count > 1 else ''}. Thank you for your purchase!"

    return schemas.WebhookResponse(success=True, message=message)
