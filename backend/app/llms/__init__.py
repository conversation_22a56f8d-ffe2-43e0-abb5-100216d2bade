from ..config import settings
from .base import ConversationalLLM
from .gemini import Gemini_LLM
from .openai import OpenAI_LLM


def get_llm() -> ConversationalLLM:
    """
    Factory function to get the appropriate LLM based on the provider setting.
    """
    if settings.llm_provider == "openai":
        return OpenAI_LLM()
    elif settings.llm_provider == "google":
        return Gemini_LLM()
    else:
        raise ValueError(f"Unsupported LLM provider: {settings.llm_provider}")
