from ..config import settings
from .base import TTSBase
from .elevenlabs import ElevenLabsTTS
from .local import LocalTTS
from .nova import NovaTTS


def get_tts_service() -> TTSBase:
    provider = settings.tts_provider
    if provider == "elevenlabs":
        return ElevenLabsTTS()
    elif provider == "nova":
        return NovaTTS()
    elif provider == "local":
        return LocalTTS()
    else:
        raise ValueError(f"Unsupported TTS provider: {provider}")
