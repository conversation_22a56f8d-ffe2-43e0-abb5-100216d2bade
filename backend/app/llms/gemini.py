import logging
from typing import AsyncGenerator, Dict, List

import google.generativeai as genai
from langchain_core.messages import AIMessageChunk

from ..config import settings
from .base import ConversationalLLM
from ..performance_tracker import track_latency


class Gemini_LLM(ConversationalLLM):
    def __init__(self):
        genai.configure(api_key=settings.gemini_api_key)
        self.model_name = settings.gemini_model

    @track_latency("llm", "gemini_chat")
    async def astream(
        self, messages: List[Dict[str, str]], stream: bool = True
    ) -> AsyncGenerator[str, None]:
        system_prompt = None
        if messages and messages[0]["role"] == "system":
            system_prompt = messages[0]["content"]
            chat_messages = messages[1:]
        else:
            chat_messages = messages

        model = genai.GenerativeModel(self.model_name, system_instruction=system_prompt)

        # Gemini API expects roles to be 'user' and 'model'
        history = [
            {
                "role": "user" if m["role"] == "user" else "model",
                "parts": [m["content"]],
            }
            for m in chat_messages
        ]

        try:
            if not stream:
                response = await model.generate_content_async(history)
                yield AIMessageChunk(content=response.text)
                return

            response_stream = await model.generate_content_async(history, stream=True)
            async for chunk in response_stream:
                yield AIMessageChunk(content=chunk.text)
        except Exception as e:
            logging.error(f"Error during Gemini API call. Input messages: {history}")
            logging.error(f"Gemini Error: {e}")
            yield AIMessageChunk(content="I'm sorry, I encountered an error.")
