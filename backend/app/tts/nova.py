import logging
import base64
import asyncio
from ..config import settings
from .base import TTS<PERSON><PERSON>
from typing import AsyncGenerator
from deepgram import DeepgramClient, SpeakOptions, SpeakWebSocketEvents
from ..performance_tracker import track_latency

logger = logging.getLogger(__name__)


class NovaTTS(TTSBase):
    def __init__(self):
        self.client = DeepgramClient(settings.nova_api_key)
        logger.info("NovaTTS (Deepgram) service initialized.")

    @track_latency("tts", "nova_stream")
    async def stream(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        logger.info("Starting Nova (Deepgram) TTS generation...")
        
        # Collect the full text from the generator to send to Deepgram, checking for interruptions
        text_parts = []
        async for chunk in text_generator:
            if interruption_event.is_set():
                logger.info("Interruption detected while collecting text, stopping TTS stream.")
                return
            text_parts.append(chunk)
        text = "".join(text_parts)

        if not text:
            return

        try:
            options = SpeakOptions(
                model=settings.nova_voice_id,
                encoding="linear16",
                sample_rate=24000,
            )

            source = {"text": text}

            response = self.client.speak.v("1").stream(source, options)

            audio_stream = response.stream

            chunk_size = 1024
            while True:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping TTS.")
                    break

                chunk = audio_stream.read(chunk_size)
                if not chunk:
                    break

                yield base64.b64encode(chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) TTS stream: {e}", exc_info=True)
            raise

    @track_latency("tts", "nova_stream_realtime")
    async def stream_realtime(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        """
        Realtime TTS streaming using Deepgram WebSocket API.
        Streams audio as text chunks become available.
        """
        logger.info("Starting Nova (Deepgram) realtime TTS streaming...")

        if not settings.nova_api_key:
            logger.error("Nova API key not configured")
            return

        dg_connection = None
        try:
            dg_connection = self.client.speak.websocket.v("1")

            audio_queue = asyncio.Queue()
            connection_closed = asyncio.Event()

            def on_open(self, open, **kwargs):
                logger.info(f"Deepgram WebSocket connection opened: {open}")

            def on_binary_data(self, data, **kwargs):
                logger.debug("Received audio chunk from Deepgram")
                audio_queue.put_nowait(base64.b64encode(data).decode("utf-8"))

            def on_close(self, close, **kwargs):
                logger.info(f"Deepgram WebSocket connection closed: {close}")
                connection_closed.set()

            def on_error(self, error, **kwargs):
                logger.error(f"Deepgram WebSocket error: {error}")
                connection_closed.set()

            dg_connection.on(SpeakWebSocketEvents.Open, on_open)
            dg_connection.on(SpeakWebSocketEvents.AudioData, on_binary_data)
            dg_connection.on(SpeakWebSocketEvents.Close, on_close)
            dg_connection.on(SpeakWebSocketEvents.Error, on_error)

            options = SpeakOptions(
                model=settings.nova_voice_id or "aura-2-thalia-en",
                encoding="linear16",
                sample_rate=24000,
            )

            if not dg_connection.start(options.to_dict()):
                logger.error("Failed to start Deepgram WebSocket connection")
                return

            async def send_text():
                try:
                    async for text_chunk in text_generator:
                        if interruption_event.is_set():
                            logger.info("Interruption detected, stopping text sending.")
                            return
                        if text_chunk.strip():
                            logger.debug(f"Sending text chunk to Deepgram: {text_chunk[:50]}...")
                            dg_connection.send_text(text_chunk)
                except Exception as e:
                    logger.error(f"Error sending text to Deepgram: {e}")
                finally:
                    if not interruption_event.is_set():
                        logger.debug("Flushing Deepgram connection.")
                        dg_connection.flush()

            async def receive_audio():
                try:
                    while not connection_closed.is_set() or not audio_queue.empty():
                        if interruption_event.is_set():
                            logger.info("Interruption detected, stopping audio reception.")
                            break
                        try:
                            audio_chunk = await asyncio.wait_for(audio_queue.get(), timeout=0.1)
                            yield audio_chunk
                            audio_queue.task_done()
                        except asyncio.TimeoutError:
                            continue
                except Exception as e:
                    logger.error(f"Error receiving audio from Deepgram: {e}")

            send_task = asyncio.create_task(send_text())
            
            async for audio_chunk in receive_audio():
                yield audio_chunk

            await send_task

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) realtime TTS stream: {e}", exc_info=True)
            raise
        finally:
            if dg_connection:
                logger.info("Finishing Deepgram connection.")
                dg_connection.finish()


    async def list_voices(self):
        logger.info("Listing Nova (Deepgram) voices is not supported via the SDK.")
        return []
