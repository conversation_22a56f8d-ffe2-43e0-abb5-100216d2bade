# E-commerce & Call Center Application

A modern full-stack application combining e-commerce functionality with AI-powered call center capabilities using Twilio and ElevenLabs.

## Features

### 🛍️ E-commerce Platform

- **Product Management**: Complete CRUD operations for products with categories, variants, and inventory tracking
- **Customer Management**: Comprehensive customer profiles with contact information and purchase history
- **Shopping Cart**: Real-time cart management with item variants and pricing
- **Search & Filtering**: Advanced product search with category filtering and suggestions

### 📞 Call Center Integration

- **Outbound Calling**: Initiate calls to customers using Twilio
- **AI Voice Agents**: Powered by ElevenLabs for natural conversation
- **Live Transcription**: Real-time call transcription and monitoring
- **Call Templates**: Pre-configured call scenarios for different use cases
- **Call History**: Complete call logs with duration and status tracking

### 🎯 Voice Agent Capabilities

- **Cart Management**: Add/remove items via voice commands
- **Product Search**: Voice-activated product catalog search
- **Customer Context**: Access to customer history and preferences
- **Webhook Integration**: Real-time data exchange during calls

## Tech Stack

### Backend

- **FastAPI**: Modern Python web framework
- **SQLAlchemy**: Database ORM with SQLite/PostgreSQL support
- **Twilio**: Voice calling and telephony services
- **ElevenLabs**: AI voice agent integration
- **Pydantic**: Data validation and serialization

### Frontend

- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **React Query**: Data fetching and caching
- **React Router**: Client-side routing
- **Lucide React**: Beautiful icon library

## Quick Start

### Prerequisites

- Python 3.12+
- [uv](https://github.com/astral-sh/uv) (fast Python package manager)
- Node.js 16+
- Twilio Account (for calling features)
- ElevenLabs Account (for voice agents)

### Backend Setup

1. **Install uv (if not already installed)**

```bash
   uv sync
```

2. **Configure environment**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize database**

   ```bash
   uv run python init_db.py
   ```

4. **Start the server**

   ```bash
   uv run python main.py
   ```

The API will be available at `http://localhost:8000`

### Frontend Setup

1. **Navigate to frontend directory**

   ```bash
   cd frontend
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:3000`

## Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Database
DATABASE_URL=sqlite:///./app.db

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key
ELEVENLABS_AGENT_ID=your_elevenlabs_agent_id

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Application Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
```

### Twilio Setup

1. **Create Twilio Account**

   - Sign up at [twilio.com](https://twilio.com)
   - Get your Account SID and Auth Token from the console
   - Purchase a phone number for outbound calls

2. **Configure Webhooks with ngrok**

   ```bash
   # Install ngrok (if not already installed)
   npm install -g ngrok

   # Start your backend server
   uv run python main.py

   # In another terminal, expose your local server
   ngrok http 8000
   ```

3. **Set Twilio Webhook URLs**

   - Copy the ngrok HTTPS URL (e.g., `https://abc123.ngrok.io`)
   - In Twilio Console, configure your phone number webhooks:
     - **Voice URL**: `https://abc123.ngrok.io/api/voice/webhook`
     - **Status Callback URL**: `https://abc123.ngrok.io/api/voice/status`

4. **Update Environment Variables**
   ```env
   TWILIO_WEBHOOK_URL=https://abc123.ngrok.io/api/voice/webhook
   NGROK_URL=https://abc123.ngrok.io
   ```

### ElevenLabs Setup

1. **Create ElevenLabs Account**

   - Sign up at [elevenlabs.io](https://elevenlabs.io)
   - Get your API key from the settings

2. **Create Conversational AI Agent**

   - Go to ConvAI section in ElevenLabs dashboard
   - Create a new agent with these settings:
     - **Voice**: Choose a natural-sounding voice
     - **Knowledge Base**: Upload product catalog and customer service info
     - **System Prompt**: Configure for e-commerce customer service

3. **Configure Agent Settings**

   ```json
   {
     "agent_id": "agent_01jzp6qcwyerm8qa685vbhq94h",
     "voice_settings": {
       "stability": 0.5,
       "similarity_boost": 0.8,
       "style": 0.2
     },
     "conversation_config": {
       "turn_detection": "server_vad",
       "language": "en"
     }
   }
   ```

4. **Set Up Dynamic Variables**
   - Configure these variables in your agent:
     - `customer_name`: Customer's name
     - `customer_phone`: Customer's phone number
     - `cart_items`: Current cart contents
     - `customer_notes`: Special customer information

### ngrok Setup for Development

ngrok is essential for testing webhooks locally:

1. **Install ngrok**

   ```bash
   # Using npm
   npm install -g ngrok

   # Or download from https://ngrok.com/download
   ```

2. **Start ngrok tunnel**

   ```bash
   # Start your backend first
   uv run python main.py

   # In another terminal
   ngrok http 8000
   ```

3. **Configure Services**

   - **Twilio**: Use ngrok URL for webhook endpoints
   - **ElevenLabs**: Configure webhook URLs if using server-side events
   - **Frontend**: Update API base URL if needed

4. **Production Deployment**
   - Replace ngrok URLs with your production domain
   - Ensure HTTPS is enabled for webhook security
   - Update all service configurations with production URLs

## API Documentation

Once the backend is running, visit:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Key Endpoints

#### Products

- `GET /api/products` - List products with filtering
- `POST /api/products` - Create new product
- `GET /api/products/{id}` - Get product details
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product

#### Customers

- `GET /api/customers` - List customers with search
- `POST /api/customers` - Create new customer
- `GET /api/customers/{id}` - Get customer details
- `GET /api/customers/{id}/cart` - Get customer cart
- `POST /api/customers/{id}/cart/items` - Add item to cart

#### Voice Calls

- `POST /api/voice/outbound-call` - Initiate outbound call
- `GET /api/voice/call-status/{call_sid}` - Get call status
- `POST /api/voice/end-call/{call_sid}` - End active call

#### Webhooks (for voice agents)

- `POST /api/webhooks/get-cart-summary` - Get cart summary
- `POST /api/webhooks/search-products` - Search products
- `POST /api/webhooks/add-cart-item` - Add item to cart
- `POST /api/webhooks/remove-cart-item` - Remove item from cart

## Usage Examples

### Making an Outbound Call

```python
import requests

# Initiate call
response = requests.post('http://localhost:8000/api/voice/outbound-call', json={
    "customer_id": 1,
    "phone_number": "+1234567890",
    "custom_first_message": "Hi John! This is Sarah from our store..."
})

call_data = response.json()
print(f"Call initiated: {call_data['call_sid']}")
```

### Adding Products via API

```python
import requests

# Create product
product_data = {
    "name": "Premium T-Shirt",
    "description": "High-quality cotton t-shirt",
    "price": 29.99,
    "category": "Clothing",
    "sizes": ["S", "M", "L", "XL"],
    "colors": ["White", "Black", "Blue"],
    "stock_quantity": 100
}

response = requests.post('http://localhost:8000/api/products', json=product_data)
product = response.json()
print(f"Product created: {product['id']}")
```

## Development

### Why uv?

This project uses [uv](https://github.com/astral-sh/uv) for Python package management because it's:

- **10-100x faster** than pip for package installation
- **Drop-in replacement** for pip with the same commands
- **Better dependency resolution** with lockfile support
- **Built in Rust** for maximum performance
- **Compatible** with existing Python projects

### Running Tests

```bash
# Backend tests
cd backend
uv run python -m pytest

# Frontend tests
cd frontend
npm test
```

### Code Formatting

```bash
# Backend
cd backend
uv run black .
uv run isort .

# Frontend
cd frontend
npm run lint
npm run format
```

### Development Commands

```bash
# Install development dependencies
cd backend
uv pip install -e .[dev]

# Run with hot reload
uv run python main.py

# Run tests with coverage
uv run pytest --cov=app

# Format code
uv run black . && uv run isort .

# Type checking
uv run mypy app/
```

## Deployment

### Using Docker

```bash
# Build and run with docker-compose
docker-compose up --build
```

### Manual Deployment

1. Set up production database (PostgreSQL recommended)
2. Configure environment variables for production
3. Build frontend: `npm run build`
4. Deploy backend with gunicorn or similar WSGI server
5. Serve frontend static files with nginx or similar

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Check the API documentation at `/docs`
- Review the code examples in this README
- Open an issue on GitHub

## Roadmap

- [ ] Real-time WebSocket integration for live call updates
- [ ] Advanced analytics and reporting
- [ ] Multi-language support for voice agents
- [ ] Integration with more payment providers
- [ ] Mobile app development
- [ ] Advanced AI features and sentiment analysis
