import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { Search, User, Phone, Mail, ShoppingCart, MapPin, Calendar, Package, Plus, Minus, X, PlusCircle, Trash2, Edit, RefreshCw, History } from 'lucide-react';
import { formatPhoneNumber, getInitials, formatCurrency, formatDateTime, debounce, formatDuration } from '../utils/helpers';
import { customerAPI, productAPI } from '../utils/api';
import toast from 'react-hot-toast';

const KeyValueDisplay = ({ title, data }) => {
  const entries = Object.entries(data);
  if (entries.length === 0) return null;

  return (
    <div className="mt-2">
      <h5 className="text-sm font-medium text-gray-800">{title}</h5>
      <pre className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md mt-1 whitespace-pre-wrap overflow-x-auto">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
};

export default function CustomerSelector({ customers, selectedCustomer, onCustomerSelect, onOrderClick }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [productSearchTerm, setProductSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editableCustomer, setEditableCustomer] = useState(null);
  const [notesString, setNotesString] = useState('');
  const [enrichmentString, setEnrichmentString] = useState('');
  const queryClient = useQueryClient();

  useEffect(() => {
    if (selectedCustomer) {
      setEditableCustomer({
        ...selectedCustomer,
        notes: (() => {
          try {
            return JSON.parse(selectedCustomer.notes || '{}');
          } catch {
            return typeof selectedCustomer.notes === 'string' && selectedCustomer.notes.trim()
              ? { note: selectedCustomer.notes }
              : {};
          }
        })(),
        enrichment: selectedCustomer.enrichment || {},
      });
    } else {
      setEditableCustomer(null);
    }
    setIsEditing(false);
  }, [selectedCustomer]);

  const updateCustomerMutation = useMutation(
    ({ customerId, data }) => customerAPI.update(customerId, data),
    {
      onSuccess: (response) => {
        toast.success('Customer details updated.');
        queryClient.invalidateQueries('customers');
        onCustomerSelect(response.data);
        setIsEditing(false);
      },
      onError: () => {
        toast.error('Failed to update customer details.');
      }
    }
  );

  const handleFieldChange = (field, value) => {
    setEditableCustomer(prev => ({ ...prev, [field]: value }));
  };

  const handleEditClick = () => {
    setNotesString(JSON.stringify(editableCustomer.notes, null, 2));
    setEnrichmentString(JSON.stringify(editableCustomer.enrichment, null, 2));
    setIsEditing(true);
  };

  const handleSaveChanges = () => {
    let notesObj, enrichmentObj;

    try {
      notesObj = notesString ? JSON.parse(notesString) : {};
    } catch (e) {
      toast.error('Invalid JSON in Call Notes.');
      return;
    }

    try {
      enrichmentObj = enrichmentString ? JSON.parse(enrichmentString) : {};
    } catch (e) {
      toast.error('Invalid JSON in Call Enrichment.');
      return;
    }
    
    const { notes, enrichment, ...rest } = editableCustomer;

    const updateData = {
      ...rest,
      notes: JSON.stringify(notesObj),
      enrichment: enrichmentObj,
    };
    
    delete updateData.id;

    updateCustomerMutation.mutate({
      customerId: selectedCustomer.id,
      data: updateData,
    });
  };

  const { data: customerCart, isLoading: isLoadingCart, refetch: refetchCart } = useQuery(
    ['customer-cart', selectedCustomer?.id],
    () => customerAPI.getCart(selectedCustomer.id),
    {
      enabled: !!selectedCustomer,
      select: (response) => {
        const items = response.data?.items ?? [];
        const total = items.reduce((acc, item) => acc + (item.price * item.quantity), 0);
        const totalQuantity = items.reduce((acc, item) => acc + item.quantity, 0);
        return { ...response.data, items, total, totalQuantity };
      },
    }
  );

  const { data: callHistory, isLoading: isLoadingHistory } = useQuery(
    ['customer-call-history', selectedCustomer?.id],
    () => customerAPI.getCallHistory(selectedCustomer.id).then(res => res.data),
    {
      enabled: !!selectedCustomer,
    }
  );

  const { data: orderHistory, isLoading: isLoadingOrderHistory } = useQuery(
    ['customer-order-history', selectedCustomer?.id],
    () => customerAPI.getOrderHistory(selectedCustomer.id).then(res => res.data),
    {
      enabled: !!selectedCustomer,
    }
  );

  const updateCartItemMutation = useMutation(
    ({ itemId, data }) => customerAPI.updateCartItem(itemId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['customer-cart', selectedCustomer?.id]);
      },
      onError: () => {
        toast.error('Failed to update cart item.');
      }
    }
  );

  const removeCartItemMutation = useMutation(
    (itemId) => customerAPI.removeCartItem(itemId),
    {
      onSuccess: () => {
        toast.success('Item removed from cart.');
        queryClient.invalidateQueries(['customer-cart', selectedCustomer?.id]);
      },
      onError: () => {
        toast.error('Failed to remove cart item.');
      }
    }
  );

  const handleQuantityChange = (item, delta) => {
    const newQuantity = item.quantity + delta;
    if (newQuantity > 0) {
      updateCartItemMutation.mutate({ itemId: item.id, data: { quantity: newQuantity } });
    } else {
      removeCartItemMutation.mutate(item.id);
    }
  };

  const handleSizeChange = (item, newSize) => {
    updateCartItemMutation.mutate({ itemId: item.id, data: { size: newSize } });
  };

  const debouncedSearch = debounce((term) => {
    setSearchTerm(term);
  }, 300);

  const filteredCustomers = customers.filter(customer => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      customer.name.toLowerCase().includes(search) ||
      customer.phone.includes(search) ||
      (customer.email && customer.email.toLowerCase().includes(search))
    );
  });

  const handleCustomerSelect = (customer) => {
    onCustomerSelect(customer);
    setIsOpen(false);
    setSearchTerm('');
  };

  const { data: productsData, isLoading: isLoadingProducts } = useQuery(
    'products',
    () => productAPI.getAll(),
    {
      enabled: isAddingProduct,
      select: (response) => response.data,
    }
  );

  const filteredProducts = productsData?.filter(product => {
    if (!productSearchTerm) return true;
    const search = productSearchTerm.toLowerCase();
    return product.name.toLowerCase().includes(search);
  });

  const handleAddProduct = (product, quantity, size) => {
    customerAPI.addToCart(selectedCustomer.id, {
      product_id: product.id,
      quantity,
      size,
    }).then(() => {
      toast.success(`${product.name} added to cart.`);
      queryClient.invalidateQueries(['customer-cart', selectedCustomer?.id]);
    }).catch(() => {
      toast.error(`Failed to add ${product.name} to cart.`);
    });
  };

  return (
    <div className="relative">
      {selectedCustomer ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                  <span className="text-lg font-medium text-primary-600">
                    {getInitials(selectedCustomer.name)}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{selectedCustomer.name}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      {formatPhoneNumber(selectedCustomer.phone)}
                    </div>
                    {selectedCustomer.email && (
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-1" />
                        {selectedCustomer.email}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(true)}
                className="btn btn-outline btn-sm"
              >
                Change Customer
              </button>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 gap-y-8">
              {/* Customer Information & Cart */}
              <div className="space-y-6">
                {/* Customer Information Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-base font-medium text-gray-900 flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Customer Information
                    </h4>
                    {!isEditing ? (
                      <button onClick={handleEditClick} className="btn btn-xs btn-outline">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </button>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <button onClick={() => setIsEditing(false)} className="btn btn-xs btn-ghost">Cancel</button>
                        <button onClick={handleSaveChanges} className="btn btn-xs btn-primary" disabled={updateCustomerMutation.isLoading}>
                          {updateCustomerMutation.isLoading ? 'Saving...' : 'Save'}
                        </button>
                      </div>
                    )}
                  </div>

                  {isEditing ? (
                    <div className="space-y-3 text-sm">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Name</label>
                          <input type="text" value={editableCustomer.name} onChange={e => handleFieldChange('name', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Phone</label>
                          <input type="text" value={editableCustomer.phone} onChange={e => handleFieldChange('phone', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div className="col-span-2">
                          <label className="block text-xs font-medium text-gray-600 mb-1">Email</label>
                          <input type="email" value={editableCustomer.email} onChange={e => handleFieldChange('email', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div className="col-span-2">
                          <label className="block text-xs font-medium text-gray-600 mb-1">Address</label>
                          <input type="text" value={editableCustomer.address} onChange={e => handleFieldChange('address', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">City</label>
                          <input type="text" value={editableCustomer.city} onChange={e => handleFieldChange('city', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">State</label>
                          <input type="text" value={editableCustomer.state} onChange={e => handleFieldChange('state', e.target.value)} className="input input-sm w-full" />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">Zip Code</label>
                          <input type="text" value={editableCustomer.zip_code} onChange={e => handleFieldChange('zip_code', e.target.value)} className="input input-sm w-full" />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3 text-sm">
                      <div className="flex items-start space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">
                          {selectedCustomer.address ? `${selectedCustomer.address}, ${selectedCustomer.city}, ${selectedCustomer.state} ${selectedCustomer.zip_code}` : 'No address provided'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
                        <span className="text-gray-600">
                          Customer since {formatDateTime(selectedCustomer.created_at, { dateOnly: true })}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  {isEditing ? (
                    <div className="space-y-4 pt-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-800 mb-1">Call Notes (JSON)</label>
                        <textarea
                          value={notesString}
                          onChange={e => setNotesString(e.target.value)}
                          className="textarea textarea-bordered w-full h-32 font-mono text-sm"
                          placeholder='Enter valid JSON for notes...'
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-800 mb-1">Call Enrichment (JSON)</label>
                        <textarea
                          value={enrichmentString}
                          onChange={e => setEnrichmentString(e.target.value)}
                          className="textarea textarea-bordered w-full h-32 font-mono text-sm"
                          placeholder='Enter valid JSON for enrichment...'
                        />
                      </div>
                    </div>
                  ) : (
                    <>
                      <KeyValueDisplay title="Call Notes" data={editableCustomer?.notes || {}} />
                      <KeyValueDisplay title="Call Enrichment" data={editableCustomer?.enrichment || {}} />
                    </>
                  )}
                </div>

                {/* Shopping Cart Section */}
                <div className="space-y-4 pt-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-base font-medium text-gray-900 flex items-center">
                      <ShoppingCart className="h-5 w-5 mr-2" />
                      Shopping Cart
                      {customerCart?.totalQuantity > 0 && (
                        <span className="ml-2 bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                          {customerCart.totalQuantity} items
                        </span>
                      )}
                    </h4>
                    <div className="flex items-center space-x-2">
                      <button
                        className="btn btn-outline btn-xs"
                        onClick={() => refetchCart()}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" /> Refresh
                      </button>
                      <button
                        className="btn btn-outline btn-xs"
                        onClick={() => setIsAddingProduct(prev => !prev)}
                      >
                        <PlusCircle className="h-3 w-3 mr-1" /> {isAddingProduct ? 'Done' : 'Add Product'}
                      </button>
                    </div>
                  </div>

                  {isAddingProduct && (
                    <div className="my-4 p-4 border rounded-lg bg-gray-50">
                      <div className="flex justify-between items-center mb-4">
                          <h5 className="text-md font-medium">Add Product</h5>
                          <div className="relative">
                              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                              <input
                                  type="text"
                                  placeholder="Search products..."
                                  className="input input-sm pl-10 w-64"
                                  value={productSearchTerm}
                                  onChange={(e) => setProductSearchTerm(e.target.value)}
                              />
                          </div>
                      </div>
                      {isLoadingProducts ? <p>Loading products...</p> : (
                          <div className="max-h-80 overflow-y-auto space-y-2 pr-2">
                              {filteredProducts?.map(product => (
                                  <div key={product.id} className="flex items-center justify-between p-2 bg-white rounded-md border">
                                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                                          <img src={product.image_url || 'https://via.placeholder.com/40'} alt={product.name} className="h-10 w-10 rounded object-cover" />
                                          <div className="flex-1 min-w-0">
                                              <p className="text-sm font-medium truncate">{product.name}</p>
                                              <p className="text-xs text-gray-500">{formatCurrency(product.price)}</p>
                                          </div>
                                      </div>
                                      <button 
                                          className="btn btn-primary btn-xs ml-2"
                                          onClick={() => handleAddProduct(product, 1, product.sizes?.[0])}
                                      >
                                          <Plus className="h-3 w-3 mr-1" /> Add
                                      </button>
                                  </div>
                              ))}
                          </div>
                      )}
                    </div>
                  )}

                  {isLoadingCart ? <p>Loading cart...</p> : customerCart?.items?.length > 0 ? (
                    <div className="space-y-3">
                      <div className="max-h-60 overflow-y-auto space-y-2 pr-2">
                        {customerCart.items.map((item) => (
                          <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-4 flex-1 min-w-0">
                              <img
                                src={item.product.image_url || 'https://via.placeholder.com/64'}
                                alt={item.product.name}
                                className="h-16 w-16 rounded-md object-cover"
                              />
                              <div className="flex-1 min-w-0">
                                <Link to={`/products/${item.product_id}`} className="text-sm font-medium text-primary-600 hover:underline truncate block">
                                  {item.product.name}
                                </Link>
                                <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                                  <div className="flex items-center">
                                    <span className="font-medium text-gray-600">Qty:</span>
                                    <button onClick={() => handleQuantityChange(item, -1)} className="btn btn-ghost btn-xs p-1 ml-1"><Minus className="h-3 w-3" /></button>
                                    <span className="w-6 text-center font-medium">{item.quantity}</span>
                                    <button onClick={() => handleQuantityChange(item, 1)} className="btn btn-ghost btn-xs p-1"><Plus className="h-3 w-3" /></button>
                                  </div>
                                  {item.product.sizes && item.product.sizes.length > 0 ? (
                                    <div className="flex items-center">
                                      <span className="font-medium text-gray-600">Size:</span>
                                      <select
                                        value={item.size || ''}
                                        onChange={(e) => handleSizeChange(item, e.target.value)}
                                        className="select select-bordered select-xs ml-2"
                                        disabled={updateCartItemMutation.isLoading}
                                      >
                                        {item.product.sizes.map(s => <option key={s} value={s}>{s}</option>)}
                                      </select>
                                    </div>
                                  ) : item.size ? (
                                    <span>Size: {item.size}</span>
                                  ) : null}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium text-gray-900 w-20 text-right">
                                {formatCurrency(item.price * item.quantity)}
                              </span>
                              <button onClick={() => removeCartItemMutation.mutate(item.id)} className="text-gray-400 hover:text-red-500">
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="border-t border-gray-200 pt-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">Total:</span>
                          <span className="text-lg font-bold text-primary-600">
                            {formatCurrency(customerCart.total)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Package className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm">Cart is empty</p>
                    </div>
                  )}
                </div>

                {/* Order History Section */}
                <div className="space-y-4 pt-4">
                  <h4 className="text-base font-medium text-gray-900 flex items-center">
                    <History className="h-5 w-5 mr-2" />
                    Order History
                  </h4>
                  {isLoadingOrderHistory ? <p>Loading order history...</p> : orderHistory?.length > 0 ? (
                    <div className="space-y-3">
                      <div className="max-h-60 overflow-y-auto space-y-2 pr-2">
                        {orderHistory.map((order) => (
                          <button key={order.id} onClick={() => onOrderClick(order)} className="w-full text-left block p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium">Order #{order.id}</p>
                              <p className="text-xs text-gray-500">{formatDateTime(order.created_at)}</p>
                            </div>
                            <p className="text-xs text-gray-600 mt-1">Status: {order.status}</p>
                            <p className="text-sm font-bold text-primary-600 mt-1">{formatCurrency(order.total_amount)}</p>
                          </button>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <p className="text-sm">No order history</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Call History */}
              <div className="space-y-4">
                <h4 className="text-base font-medium text-gray-900 flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  Call History
                </h4>
                {isLoadingHistory ? <p>Loading history...</p> : callHistory?.length > 0 ? (
                  <div className="space-y-3">
                    <div className="max-h-[40rem] overflow-y-auto space-y-2 pr-2">
                      {callHistory.map((call) => (
                        <Link key={call.id} to={`/calls/${call.id}`} className="block p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{call.call_type.charAt(0).toUpperCase() + call.call_type.slice(1)} Call (#{call.id})</p>
                            <p className="text-xs text-gray-500">{formatDateTime(call.created_at)}</p>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">Duration: {formatDuration(call.call_duration)}</p>
                          <p className="text-xs text-gray-600 mt-1">Status: {call.status}</p>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <p className="text-sm">No call history</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <button
          onClick={() => setIsOpen(true)}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-center hover:border-gray-400 transition-colors"
        >
          <User className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600">Select a customer to call</p>
        </button>
      )}

      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity"
              onClick={() => setIsOpen(false)}
            />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Select Customer</h3>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search customers..."
                      className="input pl-10"
                      onChange={(e) => debouncedSearch(e.target.value)}
                      autoFocus
                    />
                  </div>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {filteredCustomers.length > 0 ? (
                    <div className="space-y-2">
                      {filteredCustomers.map((customer) => (
                        <button
                          key={customer.id}
                          onClick={() => handleCustomerSelect(customer)}
                          className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary-600">
                                {getInitials(customer.name)}
                              </span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {customer.name}
                              </p>
                              <div className="flex items-center space-x-3 text-xs text-gray-500">
                                <span>{formatPhoneNumber(customer.phone)}</span>
                                {customer.email && <span>{customer.email}</span>}
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <User className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">
                        {searchTerm ? 'No customers found' : 'No customers available'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setIsOpen(false)}
                  className="btn btn-secondary btn-md sm:w-auto w-full"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}