import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch

from app.app import app
from app.database import get_db, Base
from app import crud, schemas
from app.stt.local import LocalSTT
from app.tts.local import LocalTTS
from tests.database import TestingSessionLocal, engine

# This test requires a Vosk model and espeak-ng to be installed.

user_prompts = [
    "what is in my cart?",
    "can you remove one blue denim jeans?",
    "thats it, thanks.",
    "yes"
]

class MockSTTWithScript(LocalSTT):
    async def transcribe(self, audio_queue: asyncio.Queue, transcript_queue: asyncio.Queue, sample_rate: int = 16000):
        for text in user_prompts:
            await asyncio.sleep(2) 
            print(f"Mock STT sending: {text}")
            await transcript_queue.put((text, True))
        
        await asyncio.sleep(5)
        await transcript_queue.put((None, None))

@pytest.fixture(scope="module")
def db_session():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    crud.create_product(db, schemas.ProductCreate(name="Classic White T-Shirt", price=29.99, description="A timeless classic."))
    crud.create_product(db, schemas.ProductCreate(name="Blue Denim Jeans", price=79.99, description="Perfect for any occasion."))
    db.commit()
    yield db
    db.close()
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="module")
def client(db_session):
    app.dependency_overrides[get_db] = lambda: db_session
    return TestClient(app)

@patch('app.routers.web_call.get_stt_service', return_value=MockSTTWithScript())
@patch('app.routers.web_call.get_tts_service', return_value=LocalTTS())
def test_web_call_e2e_real_llm(mock_tts, mock_stt, client, db_session: Session):
    # 1. Setup
    customer = crud.create_customer(db_session, schemas.CustomerCreate(name="John Smith", email="<EMAIL>", phone="1234567890"))
    tshirt = crud.get_product_by_name(db_session, "Classic White T-Shirt")
    jeans = crud.get_product_by_name(db_session, "Blue Denim Jeans")
    cart = crud.get_customer_active_cart(db_session, customer.id)
    crud.add_cart_item(db_session, cart.id, schemas.CartItemCreate(product_id=tshirt.id, quantity=1, size="XS", color="White"))
    crud.add_cart_item(db_session, cart.id, schemas.CartItemCreate(product_id=jeans.id, quantity=2, size="28", color="Blue"))
    call_history = crud.create_call_history(db_session, schemas.CallHistoryCreate(
        customer_id=customer.id, call_type="web",
        system_prompt="You are Ava, a helpful shopping assistant. Your goal is to help the user with their cart and guide them to checkout.",
        first_message="Hi {customer_name}! This is Ava, how can I help you with your cart?"
    ))
    db_session.commit()

    # 2. Simulate WebSocket
    with client.websocket_connect(f"/api/voice/web-call") as websocket:
        websocket.send_json({"type": "init", "call_history_id": call_history.id})
        try:
            while True:
                message = websocket.receive_json()
                if message.get("type") == "transcript":
                    print(f"Received transcript: {message}")
        except Exception as e:
            print(f"Websocket receive loop ended with: {e}")
            pass

    # 3. Assert final state
    db_session.refresh(cart)
    jeans_item = next((item for item in cart.items if item.product.name == "Blue Denim Jeans"), None)
    assert jeans_item is not None
    assert jeans_item.quantity == 1
    assert not cart.is_active
