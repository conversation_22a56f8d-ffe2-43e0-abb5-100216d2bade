@import "tailwindcss";

@theme {
  /* Color palette */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success-950: #052e16;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  --color-warning-950: #451a03;

  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;
  --color-danger-950: #450a0a;

  /* Semantic colors */
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #2563eb;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #ffffff;
}

@layer base {
  * {
    border-color: var(--color-border);
  }

  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
    background-color: var(--color-background);
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }

  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 active:bg-warning-700;
  }

  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }

  .btn-outline {
    border: 1px solid var(--color-input);
    background-color: var(--color-background);
  }
  .btn-outline:hover {
    background-color: var(--color-accent);
    color: var(--color-accent-foreground);
  }

  .btn-ghost:hover {
    background-color: var(--color-accent);
    color: var(--color-accent-foreground);
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  .input {
    @apply flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    border: 1px solid var(--color-input);
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
  .input::placeholder {
    color: var(--color-muted-foreground);
  }
  .input:focus-visible {
    ring-color: var(--color-ring);
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    border: 1px solid var(--color-input);
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
  .textarea::placeholder {
    color: var(--color-muted-foreground);
  }
  .textarea:focus-visible {
    ring-color: var(--color-ring);
  }

  .select {
    @apply flex h-10 w-full items-center justify-between rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    border: 1px solid var(--color-input);
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
  .select::placeholder {
    color: var(--color-muted-foreground);
  }
  .select:focus {
    ring-color: var(--color-ring);
  }

  .card {
    @apply rounded-lg shadow-sm;
    border: 1px solid var(--color-border);
    background-color: var(--color-card);
    color: var(--color-card-foreground);
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm;
    color: var(--color-muted-foreground);
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    border: 1px solid transparent;
    ring-color: var(--color-ring);
  }

  .badge-default {
    @apply border-transparent bg-primary-600 text-white hover:bg-primary-700;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200;
  }

  .badge-destructive {
    border: 1px solid transparent;
    background-color: var(--color-destructive);
    color: var(--color-destructive-foreground);
  }
  .badge-destructive:hover {
    background-color: color-mix(
      in srgb,
      var(--color-destructive) 80%,
      transparent
    );
  }

  .badge-outline {
    color: var(--color-foreground);
    border: 1px solid var(--color-border);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

/* Fade in animation */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation for call status */
.pulse-call {
  animation: pulseCall 2s infinite;
}

@keyframes pulseCall {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
