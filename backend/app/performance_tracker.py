import time
from functools import wraps
from typing import Any, Callable, Dict, List
from sqlalchemy.orm import Session
from . import crud, schemas
from .database import SessionLocal

def track_latency(service: str, action: str) -> Callable:
    """
    A decorator to measure and record the latency of function calls into the database.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            latency = end_time - start_time

            db: Session = SessionLocal()
            try:
                metric = schemas.PerformanceMetricCreate(
                    service=service,
                    action=action,
                    latency=latency,
                )
                crud.create_performance_metric(db, metric)
            finally:
                db.close()

            return result
        return wrapper
    return decorator