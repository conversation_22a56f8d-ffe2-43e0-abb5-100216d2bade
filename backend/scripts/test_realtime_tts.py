#!/usr/bin/env python3
"""
Test script for realtime TTS functionality.
This script demonstrates how to use the new realtime streaming methods.
"""

import asyncio
import logging
import os
import sys
from typing import AsyncGenerator

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import settings
from app.tts.nova import NovaTTS
from app.tts.elevenlabs import ElevenLabsTTS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def text_chunk_generator(text: str, chunk_size: int = 10) -> AsyncGenerator[str, None]:
    """
    Simulates streaming text by yielding chunks of the input text.
    This mimics how text would arrive from an LLM in real-time.
    """
    words = text.split()
    for i in range(0, len(words), chunk_size):
        chunk = " ".join(words[i:i + chunk_size])
        logger.info(f"Yielding text chunk: '{chunk}'")
        yield chunk + " "
        # Simulate delay between chunks (like LLM generation)
        await asyncio.sleep(0.5)


async def test_nova_realtime():
    """Test Nova (Deepgram) realtime TTS streaming."""
    logger.info("Testing Nova (Deepgram) realtime TTS...")
    
    if not settings.nova_api_key:
        logger.warning("Nova API key not configured, skipping Nova test")
        return
    
    try:
        tts = NovaTTS()
        interruption_event = asyncio.Event()
        
        test_text = "Hello, this is a test of the Nova realtime text-to-speech streaming functionality. The audio should start playing as soon as the first chunk of text is processed, rather than waiting for the entire text to be complete."
        
        text_gen = text_chunk_generator(test_text)
        audio_chunks = []
        
        logger.info("Starting Nova realtime TTS streaming...")
        async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
            logger.info(f"Received audio chunk of length: {len(audio_chunk)}")
            audio_chunks.append(audio_chunk)
            
            # For demo purposes, stop after receiving a few chunks
            if len(audio_chunks) >= 5:
                logger.info("Stopping after 5 audio chunks for demo")
                interruption_event.set()
                break
        
        logger.info(f"Nova realtime test completed. Received {len(audio_chunks)} audio chunks.")
        
    except Exception as e:
        logger.error(f"Error testing Nova realtime TTS: {e}", exc_info=True)


async def test_elevenlabs_realtime():
    """Test ElevenLabs realtime TTS streaming."""
    logger.info("Testing ElevenLabs realtime TTS...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.warning("ElevenLabs API key or voice ID not configured, skipping ElevenLabs test")
        return
    
    try:
        tts = ElevenLabsTTS()
        interruption_event = asyncio.Event()
        
        test_text = "Hello, this is a test of the ElevenLabs realtime text-to-speech streaming functionality. The audio should start playing as soon as the first chunk of text is processed, providing much lower latency than traditional batch processing."
        
        text_gen = text_chunk_generator(test_text)
        audio_chunks = []
        
        logger.info("Starting ElevenLabs realtime TTS streaming...")
        async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
            logger.info(f"Received audio chunk of length: {len(audio_chunk)}")
            audio_chunks.append(audio_chunk)
            
            # For demo purposes, stop after receiving a few chunks
            if len(audio_chunks) >= 5:
                logger.info("Stopping after 5 audio chunks for demo")
                interruption_event.set()
                break
        
        logger.info(f"ElevenLabs realtime test completed. Received {len(audio_chunks)} audio chunks.")
        
    except Exception as e:
        logger.error(f"Error testing ElevenLabs realtime TTS: {e}", exc_info=True)


async def test_comparison():
    """Compare regular vs realtime streaming."""
    logger.info("Comparing regular vs realtime streaming...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.warning("ElevenLabs not configured, skipping comparison test")
        return
    
    try:
        tts = ElevenLabsTTS()
        interruption_event = asyncio.Event()
        
        test_text = "This is a comparison test between regular and realtime streaming."
        
        # Test regular streaming
        logger.info("Testing regular streaming...")
        start_time = asyncio.get_event_loop().time()
        text_gen = text_chunk_generator(test_text, chunk_size=100)  # Large chunks for regular
        audio_chunks_regular = []
        
        async for audio_chunk in tts.stream(text_gen, interruption_event):
            audio_chunks_regular.append(audio_chunk)
        
        regular_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"Regular streaming: {len(audio_chunks_regular)} chunks in {regular_time:.2f}s")
        
        # Reset interruption event
        interruption_event.clear()
        
        # Test realtime streaming
        logger.info("Testing realtime streaming...")
        start_time = asyncio.get_event_loop().time()
        text_gen = text_chunk_generator(test_text, chunk_size=3)  # Small chunks for realtime
        audio_chunks_realtime = []
        
        async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
            audio_chunks_realtime.append(audio_chunk)
        
        realtime_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"Realtime streaming: {len(audio_chunks_realtime)} chunks in {realtime_time:.2f}s")
        
        logger.info("Comparison completed!")
        
    except Exception as e:
        logger.error(f"Error in comparison test: {e}", exc_info=True)


async def main():
    """Main test function."""
    logger.info("Starting realtime TTS tests...")
    
    # Test Nova realtime streaming
    await test_nova_realtime()
    
    # Test ElevenLabs realtime streaming
    await test_elevenlabs_realtime()
    
    # Test comparison between regular and realtime
    await test_comparison()
    
    logger.info("All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
