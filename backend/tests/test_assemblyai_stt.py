import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

# Ensure the app module is in the Python path
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.stt.assemblyai import AssemblyAI
from app.config import settings

# Mock the assemblyai library
aai_mock = MagicMock()

@patch('app.stt.assemblyai.aai', aai_mock)
@pytest.mark.asyncio
async def test_assemblyai_transcription_flow():
    """
    Tests the basic flow of the AssemblyAI STT service, ensuring it
    connects, streams, and closes correctly without making real API calls.
    """
    # Arrange
    settings.assemblyai_api_key = "fake_key" # Needs a dummy key

    mock_transcriber = AsyncMock()
    aai_mock.RealtimeTranscriber.return_value = mock_transcriber

    audio_queue = asyncio.Queue()
    transcript_queue = asyncio.Queue()
    stt_service = AssemblyAI()

    # Act
    # Run the transcription service as a background task
    transcribe_task = asyncio.create_task(
        stt_service.transcribe(audio_queue, transcript_queue)
    )

    # Simulate sending audio chunks
    await audio_queue.put(b'\x00\x00')
    await audio_queue.put(b'\x01\x01')
    await audio_queue.put(None) # Signal end of audio

    # Allow the task to process the queue
    await asyncio.sleep(0.1)

    # Assert
    # Check that the service attempted to connect and stream
    mock_transcriber.connect.assert_awaited_once()
    assert mock_transcriber.stream.await_count == 2
    
    # Ensure the final (None, None) is put in the queue
    final_signal = await asyncio.wait_for(transcript_queue.get(), timeout=1)
    assert final_signal == (None, None)

    # Ensure the connection is closed
    await asyncio.wait_for(transcribe_task, timeout=1) # Wait for the task to finish
    mock_transcriber.close.assert_awaited_once()

    print("\nTest PASSED: AssemblyAI STT service flow is correct.")
