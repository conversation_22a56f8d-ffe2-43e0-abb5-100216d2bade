import asyncio
import logging
from typing import Any

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.database import get_db
from app.websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/voice", tags=["text_chat"])


async def text_input_handler(websocket: WebSocket) -> str | None:
    """
    Handles incoming text messages from the WebSocket.
    Returns the text of a user_message, or None if the client disconnects.
    """
    try:
        while True:
            user_message_json = await websocket.receive_json()
            if user_message_json.get("type") == "user_message":
                return user_message_json.get("text")
            # Ignore other message types and wait for the next message
    except WebSocketDisconnect:
        logger.info("Client disconnected from text chat.")
        return None  # Signal the conversation manager to end the loop


async def text_output_handler(websocket: WebSocket, text: str):
    """Handles outgoing text messages to the WebSocket."""
    await websocket.send_json(
        {
            "type": "transcript",
            "sender": "agent",
            "transcript": text,
        }
    )


async def first_message_handler(websocket: WebSocket, text: str):
    """Handles the first message in a text chat."""
    await text_output_handler(websocket, text)


async def tool_handler(websocket: WebSocket, tool_call: dict):
    """Handles tool calls from the Langchain agent."""
    await websocket.send_json(
        {
            "type": "tool_call",
            "tool_name": tool_call["name"],
            "tool_args": tool_call["args"],
        }
    )


@router.websocket("/text-chat-ws")
async def text_chat_ws_endpoint(websocket: WebSocket, db: Session = Depends(get_db)):
    manager = WebSocketManager(websocket, db)

    await manager.handle_connection(
        input_handler=text_input_handler,
        output_handler=lambda text: text_output_handler(websocket, text),
        first_message_handler=lambda text: first_message_handler(websocket, text),
        tool_handler=lambda tool_call: tool_handler(websocket, tool_call),
    )
