from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app import models

def test_create_product(client: TestClient):
    response = client.post(
        "/api/products",
        json={"name": "Test Product", "price": 10.0, "description": "A test product"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Product"
    assert data["price"] == 10.0
    assert "id" in data

def test_read_products(client: TestClient, db_session: Session):
    client.post(
        "/api/products",
        json={"name": "Test Product", "price": 10.0, "description": "A test product"},
    )
    response = client.get("/api/products")
    assert response.status_code == 200
    data = response.json()
    assert len(data) > 0
    assert data[0]["name"] == "Test Product"

def test_read_product(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/products",
        json={"name": "Test Product", "price": 10.0, "description": "A test product"},
    )
    product_id = post_response.json()["id"]
    response = client.get(f"/api/products/{product_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Product"
    assert data["id"] == product_id

def test_read_product_not_found(client: TestClient):
    response = client.get("/api/products/999")
    assert response.status_code == 404
    assert response.json() == {"detail": "Product not found"}

def test_update_product(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/products",
        json={"name": "Test Product", "price": 10.0, "description": "A test product"},
    )
    product_id = post_response.json()["id"]
    response = client.put(
        f"/api/products/{product_id}",
        json={"name": "Updated Product"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Product"
    assert data["id"] == product_id

def test_delete_product(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/products",
        json={"name": "Test Product", "price": 10.0, "description": "A test product"},
    )
    product_id = post_response.json()["id"]
    response = client.delete(f"/api/products/{product_id}")
    assert response.status_code == 200
    assert response.json() == {"message": "Product deleted successfully"}

    # Verify the product is marked as inactive
    db_product = db_session.query(models.Product).filter(models.Product.id == product_id).first()
    assert db_product.is_active is False

def test_get_product_categories(client: TestClient, db_session: Session):
    client.post(
        "/api/products",
        json={"name": "Product 1", "price": 10.0, "category": "Category A"},
    )
    client.post(
        "/api/products",
        json={"name": "Product 2", "price": 20.0, "category": "Category B"},
    )
    client.post(
        "/api/products",
        json={"name": "Product 3", "price": 30.0, "category": "Category A"},
    )
    response = client.get("/api/products/categories/list")
    assert response.status_code == 200
    data = response.json()
    assert sorted(data) == ["Category A", "Category B"]

def test_get_search_suggestions(client: TestClient, db_session: Session):
    client.post(
        "/api/products",
        json={"name": "Laptop Pro", "price": 1200.0},
    )
    client.post(
        "/api/products",
        json={"name": "Laptop Sleeve", "price": 40.0},
    )
    response = client.get("/api/products/search/suggestions?query=Laptop")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["name"] == "Laptop Pro"
    assert data[1]["name"] == "Laptop Sleeve"
