import React from 'react';

export default function ContextPreviewModal({
  isOpen,
  onClose,
  systemPrompt,
  firstMessage,
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">LLM Context Preview</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800 text-3xl leading-none">&times;</button>
        </div>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-800">System Prompt</h3>
            <pre className="bg-gray-100 p-3 rounded-md text-sm text-gray-700 overflow-auto max-h-80 whitespace-pre-wrap">
              {systemPrompt || 'Generating preview...'}
            </pre>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-800">First Message</h3>
            <pre className="bg-gray-100 p-3 rounded-md text-sm text-gray-700 overflow-auto max-h-40 whitespace-pre-wrap">
              {firstMessage || 'Generating preview...'}
            </pre>
          </div>
        </div>
        <div className="mt-6 text-right">
          <button onClick={onClose} className="btn btn-secondary">
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
