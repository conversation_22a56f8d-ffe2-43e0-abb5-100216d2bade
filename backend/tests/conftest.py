import pytest
import os
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.app import app
from app.database import get_db, Base, engine
from tests.database import init_test_db

@pytest.fixture(scope="function")
def db_session():
    """
    Fixture to provide a database session for tests, with setup and teardown.
    """
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    
    db = next(get_db())
    init_test_db(db)
    
    try:
        yield db
    finally:
        db.close()
        del os.environ["DATABASE_URL"]

@pytest.fixture(scope="function")
def client(db_session):
    """
    Fixture to provide a TestClient instance with an overridden database session.
    """
    app.dependency_overrides[get_db] = lambda: db_session
    with TestClient(app) as c:
        yield c
    app.dependency_overrides.clear()
