import os
from typing import AsyncGenerator, Dict, List

from langchain_core.messages import AIMessageChunk
from openai import Async<PERSON>penA<PERSON>

from ..config import settings
from .base import ConversationalLLM
from ..performance_tracker import track_latency


class OpenAI_LLM(ConversationalLLM):
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model

    @track_latency("llm", "openai_chat")
    async def astream(
        self, messages: List[Dict[str, str]], stream: bool = True
    ) -> AsyncGenerator[str, None]:
        if not stream:
            response = await self.client.chat.completions.create(
                model=self.model, messages=messages, stream=False
            )
            yield AIMessageChunk(content=response.choices[0].message.content)
            return

        stream_response = await self.client.chat.completions.create(
            model=self.model, messages=messages, stream=True
        )
        async for chunk in stream_response:
            if chunk.choices[0].delta.content is not None:
                yield AIMessageChunk(content=chunk.choices[0].delta.content)
