import React from 'react';
import { Lightbulb, Zap } from 'lucide-react';

export default function Insights({ insights, actions, isActive }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Lightbulb className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Insights & Actions</h3>
          {isActive && (
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-600">Analyzing...</span>
            </div>
          )}
        </div>
      </div>
      <div className="h-48 overflow-y-auto p-4">
        {isActive || (insights && insights.length > 0) || (actions && actions.length > 0) ? (
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Real-time Insights</h4>
              {insights && insights.length > 0 ? (
                <ul className="space-y-2">
                  {insights.map((insight, index) => (
                    <li key={index} className="flex items-start">
                      <Lightbulb className="h-4 w-4 text-yellow-500 mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm text-gray-800">{insight}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-400">No insights generated yet.</p>
              )}
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Suggested Actions</h4>
              {actions && actions.length > 0 ? (
                <ul className="space-y-2">
                  {actions.map((action, index) => (
                    <li key={index} className="flex items-start">
                      <Zap className="h-4 w-4 text-blue-500 mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm text-gray-800">{action}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-400">No actions suggested yet.</p>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Lightbulb className="h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Insights will appear here
            </h4>
            <p className="text-sm text-gray-500">
              Start a call to get real-time analysis and suggested actions.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
