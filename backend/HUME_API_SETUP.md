# Hume API Key Setup

This document provides instructions on how to set up the `HUME_API_KEY` for the backend service.

## 1. Get your Hume API Key

1.  Go to the [Hume AI website](https://hume.ai/) and create an account.
2.  Navigate to the API Keys section of your account settings.
3.  Generate a new API key.

## 2. Configure the API Key in the Project

The backend service uses a `.env` file to manage environment variables. You need to add your Hume API key to this file.

1.  In the `backend` directory, create a copy of the `.env.example` file and name it `.env`.
2.  Open the `.env` file and you will see the following line:

    ```
    HUME_API_KEY=your_hume_api_key
    ```

3.  Replace `your_hume_api_key` with the API key you obtained from the Hume AI website.

4.  Save the `.env` file.

The application will now be able to authenticate with the Hume AI API and use its services.
