from abc import ABC, abstractmethod
from typing import Async<PERSON>enerator
import asyncio


class TTSBase(ABC):
    """Abstract base class for text-to-speech services."""

    @abstractmethod
    async def stream(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        """
        Streams audio from a text generator.

        Args:
            text_generator: An async generator that yields text chunks.
            interruption_event: An event to signal interruption.
        """
        pass

    async def stream_realtime(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        """
        Realtime streaming audio from a text generator using WebSocket connections.
        Default implementation falls back to regular streaming.

        Args:
            text_generator: An async generator that yields text chunks.
            interruption_event: An event to signal interruption.
        """
        # Default implementation falls back to regular streaming
        # Subclasses should override this for true realtime streaming
        async for chunk in self.stream(text_generator, interruption_event):
            yield chunk
