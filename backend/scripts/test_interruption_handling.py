#!/usr/bin/env python3
"""
Test script to verify interruption handling in TTS streaming.
This simulates interrupting TTS mid-stream to ensure proper cleanup.
"""

import asyncio
import logging
import os
import sys
from typing import AsyncGenerator

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import settings
from app.tts.elevenlabs import ElevenLabsTTS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def long_text_generator() -> AsyncGenerator[str, None]:
    """Generate a long text that can be interrupted."""
    text = """
    This is a very long text that will be used to test interruption handling.
    We want to make sure that when the TTS stream is interrupted, it handles
    the interruption gracefully without causing audio errors or corruption.
    The text should be long enough to generate multiple audio chunks so we
    can test interrupting at different points in the stream. This helps us
    verify that the audio processing pipeline can handle partial data and
    incomplete streams without crashing or producing invalid audio files.
    """
    
    words = text.split()
    for i, word in enumerate(words):
        yield word + " "
        await asyncio.sleep(0.1)  # Simulate streaming delay
        
        # Simulate interruption after 10 words
        if i == 10:
            logger.info("Simulating interruption after 10 words...")
            break


async def test_interruption_handling():
    """Test TTS interruption handling."""
    logger.info("Testing TTS interruption handling...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.error("ElevenLabs API key or voice ID not configured")
        return False
    
    try:
        tts = ElevenLabsTTS()
        interruption_event = asyncio.Event()
        
        # Start the TTS stream
        text_gen = long_text_generator()
        chunk_count = 0
        
        # Create a task to interrupt after a delay
        async def interrupt_after_delay():
            await asyncio.sleep(2.0)  # Wait 2 seconds
            logger.info("Setting interruption event...")
            interruption_event.set()
        
        interrupt_task = asyncio.create_task(interrupt_after_delay())
        
        try:
            async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
                chunk_count += 1
                logger.info(f"Received audio chunk {chunk_count}: {len(audio_chunk)} chars")
                
                # Check if we should stop due to interruption
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping stream...")
                    break
                    
                # Safety limit to prevent infinite loop
                if chunk_count > 20:
                    logger.warning("Reached safety limit, stopping...")
                    break
                    
        except Exception as e:
            logger.error(f"Error during streaming: {e}")
            return False
        finally:
            # Clean up the interrupt task
            if not interrupt_task.done():
                interrupt_task.cancel()
                try:
                    await interrupt_task
                except asyncio.CancelledError:
                    pass
        
        logger.info(f"Stream completed with {chunk_count} chunks before interruption")
        
        # Verify that the interruption was handled properly
        if interruption_event.is_set() and chunk_count > 0:
            logger.info("✅ Interruption handling test passed!")
            return True
        else:
            logger.error("❌ Interruption handling test failed!")
            return False
        
    except Exception as e:
        logger.error(f"Error testing interruption handling: {e}", exc_info=True)
        return False


async def test_multiple_interruptions():
    """Test multiple rapid interruptions."""
    logger.info("Testing multiple rapid interruptions...")
    
    if not settings.elevenlabs_api_key or not settings.elevenlabs_voice_id:
        logger.error("ElevenLabs API key or voice ID not configured")
        return False
    
    try:
        tts = ElevenLabsTTS()
        
        for i in range(3):
            logger.info(f"Starting interruption test {i+1}/3...")
            
            interruption_event = asyncio.Event()
            text_gen = long_text_generator()
            chunk_count = 0
            
            # Interrupt after different delays
            interrupt_delay = 0.5 + (i * 0.5)  # 0.5s, 1.0s, 1.5s
            
            async def interrupt_after_delay():
                await asyncio.sleep(interrupt_delay)
                interruption_event.set()
            
            interrupt_task = asyncio.create_task(interrupt_after_delay())
            
            try:
                async for audio_chunk in tts.stream_realtime(text_gen, interruption_event):
                    chunk_count += 1
                    
                    if interruption_event.is_set():
                        break
                        
                    if chunk_count > 10:  # Safety limit
                        break
                        
            except Exception as e:
                logger.error(f"Error in test {i+1}: {e}")
                return False
            finally:
                if not interrupt_task.done():
                    interrupt_task.cancel()
                    try:
                        await interrupt_task
                    except asyncio.CancelledError:
                        pass
            
            logger.info(f"Test {i+1} completed with {chunk_count} chunks")
            
            # Small delay between tests
            await asyncio.sleep(0.5)
        
        logger.info("✅ Multiple interruptions test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing multiple interruptions: {e}", exc_info=True)
        return False


async def main():
    """Main test function."""
    logger.info("Starting TTS interruption handling tests...")
    
    # Test 1: Basic interruption handling
    success1 = await test_interruption_handling()
    if not success1:
        logger.error("Basic interruption test failed!")
        return
    
    # Small delay between tests
    await asyncio.sleep(1.0)
    
    # Test 2: Multiple rapid interruptions
    success2 = await test_multiple_interruptions()
    if not success2:
        logger.error("Multiple interruptions test failed!")
        return
    
    logger.info("🎉 All interruption handling tests passed!")


if __name__ == "__main__":
    asyncio.run(main())
