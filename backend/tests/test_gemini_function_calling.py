import pytest
from unittest.mock import patch, Magic<PERSON>ock

# Mock the google.genai module and its Client
# We need to mock the entire path to the Client and its methods
# The actual imports from the user's example will be inside the test function
# to ensure the patch is active when the code under test is executed.

def test_gemini_function_calling_example():
    """
    Tests the function calling example provided by the user.
    Mocks the Gemini API response to simulate a function call.
    """
    # Mock the response from client.models.generate_content
    mock_function_call = MagicMock()
    mock_function_call.name = "get_current_temperature"
    mock_function_call.args = {"location": "London"}

    mock_part = MagicMock()
    mock_part.function_call = mock_function_call
    mock_part.text = None # Ensure text is None when function_call is present

    mock_content = MagicMock()
    mock_content.parts = [mock_part]

    mock_candidate = MagicMock()
    mock_candidate.content = mock_content

    mock_response = MagicMock()
    mock_response.candidates = [mock_candidate]
    mock_response.text = None # Ensure text is None when function_call is present

    with patch("google.genai.Client") as MockClient:
        # Configure the mock client to return our mock response
        MockClient.return_value.models.generate_content.return_value = mock_response

        # Now, import and run the user's example code
        from google import genai
        from google.genai import types

        # Define the function declaration for the model
        weather_function = {
            "name": "get_current_temperature",
            "description": "Gets the current temperature for a given location.",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city name, e.g. San Francisco",
                    },
                },
                "required": ["location"],
            },
        }

        # Configure the client and tools
        client = genai.Client()
        tools = types.Tool(function_declarations=[weather_function])
        config = types.GenerateContentConfig(tools=[tools])

        # Send request with function declarations
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="What's the temperature in London?",
            config=config,
        )

        # Check for a function call
        if response.candidates[0].content.parts[0].function_call:
            function_call = response.candidates[0].content.parts[0].function_call
            print(f"Function to call: {function_call.name}")
            print(f"Arguments: {function_call.args}")
            # Assertions to verify the function call
            assert function_call.name == "get_current_temperature"
            assert function_call.args == {"location": "London"}
        else:
            print("No function call found in the response.")
            print(response.text)
            pytest.fail("Expected a function call, but none was found.")

