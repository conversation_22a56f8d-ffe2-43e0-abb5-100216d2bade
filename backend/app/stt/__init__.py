from ..config import settings
from .assemblyai import AssemblyAI
from .base import STTBase
from .local import LocalSTT
from .nova import NovaSTT


def get_stt_service() -> STTBase:
    provider = settings.stt_provider
    if provider == "assemblyai":
        return AssemblyAI()
    elif provider == "nova":
        return NovaSTT()
    elif provider == "local":
        return LocalSTT()
    else:
        raise ValueError(f"Unsupported STT provider: {provider}")
