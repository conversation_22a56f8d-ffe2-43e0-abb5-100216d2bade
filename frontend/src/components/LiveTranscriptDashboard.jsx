import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Phone, User, Bo<PERSON> } from 'lucide-react';

export default function LiveTranscriptDashboard() {
  const [transcript, setTranscript] = useState([]);
  const [isCallActive, setIsCallActive] = useState(false);
  const [currentCustomer, setCurrentCustomer] = useState(null);

  // Simulate real-time transcript updates
  useEffect(() => {
    // Listen for transcript updates from ElevenLabs widget
    const handleTranscriptUpdate = (event) => {
      if (event.detail) {
        const entry = {
          id: Date.now() + Math.random(),
          speaker: event.detail.speaker,
          text: event.detail.text,
          timestamp: new Date().toLocaleTimeString(),
          customer: event.detail.customer || 'Unknown Customer'
        };

        setTranscript(prev => [...prev.slice(-9), entry]); // Keep last 10 entries
        setIsCallActive(true);
        setCurrentCustomer(event.detail.customer);
      }
    };

    // Listen for call status updates
    const handleCallStatus = (event) => {
      if (event.detail) {
        setIsCallActive(event.detail.active);
        if (!event.detail.active) {
          setCurrentCustomer(null);
        }
      }
    };

    // Add event listeners
    window.addEventListener('elevenlabs-transcript-update', handleTranscriptUpdate);
    window.addEventListener('call-status-update', handleCallStatus);

    // Cleanup
    return () => {
      window.removeEventListener('elevenlabs-transcript-update', handleTranscriptUpdate);
      window.removeEventListener('call-status-update', handleCallStatus);
    };
  }, []);

  if (transcript.length === 0 && !isCallActive) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Live Transcript</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <MicOff className="h-4 w-4" />
            <span>No active calls</span>
          </div>
        </div>
        <div className="text-center py-8 text-gray-500">
          <Phone className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-sm">Live conversation transcript will appear here</p>
          <p className="text-xs text-gray-400 mt-1">Start a call to see real-time conversation</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Live Transcript</h3>
        <div className="flex items-center space-x-2">
          {isCallActive && (
            <div className="flex items-center space-x-2 text-sm text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <Mic className="h-4 w-4" />
              <span>Live</span>
            </div>
          )}
          {currentCustomer && (
            <div className="text-sm text-gray-600">
              with {currentCustomer}
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {transcript.map((entry) => (
          <div key={entry.id} className="flex space-x-3">
            <div className="flex-shrink-0">
              {entry.speaker === 'user' ? (
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <User className="h-4 w-4 text-blue-600" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                  <Bot className="h-4 w-4 text-green-600" />
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900">
                  {entry.speaker === 'user' ? (entry.customer || 'Customer') : 'AI Assistant'}
                </p>
                <p className="text-xs text-gray-500">{entry.timestamp}</p>
              </div>
              <p className="text-sm text-gray-700 mt-1">{entry.text}</p>
            </div>
          </div>
        ))}
      </div>

      {isCallActive && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-pulse"></div>
            <span>Conversation in progress</span>
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      )}
    </div>
  );
}
