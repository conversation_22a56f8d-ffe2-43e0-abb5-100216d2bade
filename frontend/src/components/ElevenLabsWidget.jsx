import React, { useState, useEffect, useRef } from 'react';
import { Mic } from 'lucide-react';

export default function ElevenLabsWidget({ 
  customerName, 
  phoneNumber, 
  agentId = "agent_01jzp6qcwyerm8qa685vbhq94h", // Demo agent ID
  onTranscriptUpdate 
}) {
  const [isCallActive, setIsCallActive] = useState(false);
  const [transcript, setTranscript] = useState([]);
  const [callStatus, setCallStatus] = useState('ready'); // ready, connecting, active, ended
  const widgetRef = useRef(null);

  useEffect(() => {
    if (widgetRef.current) {
      initializeWidget();
    }
  }, [customerName, phoneNumber]);

  const initializeWidget = () => {
    if (!widgetRef.current) return;

    // Clear any existing widget
    widgetRef.current.innerHTML = '';

    // Create dynamic variables for the conversation
    const dynamicVariables = {
      customer_name: customerName || 'Customer',
      customer_phone: phoneNumber || 'Not provided',
      cart_items: 'Loading cart information...',
      customer_notes: 'No special notes'
    };

    // Create the ElevenLabs widget
    const widgetHTML = `
      <elevenlabs-convai
        agent-id="${agentId}"
        dynamic-variables='${JSON.stringify(dynamicVariables)}'
        override-prompt="You are a helpful customer service representative for an e-commerce store. You can help customers with their orders, answer questions about products, and assist with their shopping cart. Be friendly, professional, and helpful."
        override-first-message="Hi ${customerName || 'there'}! This is your customer service assistant. How can I help you today?"
        variant="expanded"
        width="100%"
        height="400px"
        start-call-text="Start Conversation"
        end-call-text="End Call"
        listening-text="Listening..."
        speaking-text="AI Assistant speaking..."
        style="width: 100%; height: 100%; border: none; border-radius: 8px;">
      </elevenlabs-convai>
    `;

    widgetRef.current.innerHTML = widgetHTML;

    // Set up event listeners
    setTimeout(() => {
      const widget = widgetRef.current.querySelector('elevenlabs-convai');
      if (widget) {
        widget.addEventListener('elevenlabs-convai:call', (event) => {
          console.log('ElevenLabs call started:', event);
          setIsCallActive(true);
          setCallStatus('active');
          addTranscriptEntry('system', 'Call started');
        });

        widget.addEventListener('elevenlabs-convai:end', (event) => {
          console.log('ElevenLabs call ended:', event);
          setIsCallActive(false);
          setCallStatus('ended');
          addTranscriptEntry('system', 'Call ended');
        });

        widget.addEventListener('elevenlabs-convai:error', (event) => {
          console.error('ElevenLabs error:', event);
          setCallStatus('error');
          addTranscriptEntry('system', 'Error occurred during call');
        });

        // Listen for transcript updates (if available)
        widget.addEventListener('elevenlabs-convai:transcript', (event) => {
          if (event.detail) {
            addTranscriptEntry(event.detail.speaker, event.detail.text);
          }
        });
      }
    }, 1000);
  };

  const addTranscriptEntry = (speaker, text) => {
    const entry = {
      id: Date.now(),
      speaker,
      text,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setTranscript(prev => [...prev, entry]);
    
    // Notify parent component
    if (onTranscriptUpdate) {
      onTranscriptUpdate(entry);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-xl w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Voice Assistant</h3>
          <p className="text-sm text-gray-500">
            {customerName && `Talking with ${customerName}`}
          </p>
        </div>
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium ${
          callStatus === 'active' ? 'bg-green-100 text-green-800' :
          callStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
          callStatus === 'error' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {callStatus === 'active' && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
          <span className="capitalize">{callStatus}</span>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        {/* Transcript Panel */}
        <div className="w-2/5 border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">Live Transcript</h4>
          </div>
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {transcript.length === 0 ? (
              <div className="text-center text-gray-500 text-sm">
                <Mic className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p>Transcript will appear here during the conversation</p>
              </div>
            ) : (
              transcript.map((entry) => (
                <div key={entry.id} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className={`text-xs font-medium ${
                      entry.speaker === 'user' ? 'text-blue-600' :
                      entry.speaker === 'assistant' ? 'text-green-600' :
                      'text-gray-500'
                    }`}>
                      {entry.speaker === 'user' ? customerName || 'Customer' :
                       entry.speaker === 'assistant' ? 'AI Assistant' :
                       'System'}
                    </span>
                    <span className="text-xs text-gray-400">{entry.timestamp}</span>
                  </div>
                  <p className="text-sm text-gray-700">{entry.text}</p>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Widget Panel */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">Voice Interface</h4>
            <p className="text-xs text-gray-500 mt-1">Click the microphone to start talking</p>
          </div>
          <div className="flex-1 p-4">
            <div 
              ref={widgetRef}
              className="w-full h-full bg-gray-50 rounded-lg flex items-center justify-center"
            >
              <div className="text-center text-gray-500">
                <Mic className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>Initializing voice assistant...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
