import logging
from typing import Dict, List

from fastapi import WebSocket

logger = logging.getLogger(__name__)


class GlobalWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[int, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, call_history_id: int):
        await websocket.accept()
        if call_history_id not in self.active_connections:
            self.active_connections[call_history_id] = []
        self.active_connections[call_history_id].append(websocket)
        logger.info(
            f"Frontend WebSocket connected for call_history_id: {call_history_id}"
        )

    def disconnect(self, websocket: WebSocket, call_history_id: int):
        if call_history_id in self.active_connections:
            self.active_connections[call_history_id].remove(websocket)
            if not self.active_connections[call_history_id]:
                del self.active_connections[call_history_id]
        logger.info(
            f"Frontend WebSocket disconnected for call_history_id: {call_history_id}"
        )

    async def broadcast(self, call_history_id: int, message: dict):
        if call_history_id in self.active_connections:
            for connection in self.active_connections[call_history_id]:
                await connection.send_json(message)


manager = GlobalWebSocketManager()
