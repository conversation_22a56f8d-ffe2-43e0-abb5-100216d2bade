#!/usr/bin/env python3
"""
Setup script for the backend using uv
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {cmd}")
        print(f"Error: {e.stderr}")
        return None

def check_python_version():
    """Check if Python 3.12+ is installed"""
    try:
        if sys.version_info >= (3, 12):
            print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
            return True
        else:
            print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} is installed, but Python 3.12+ is required")
            return False
    except Exception as e:
        print(f"❌ Error checking Python version: {e}")
        return False

def check_uv_installed():
    """Check if uv is installed"""
    result = run_command("uv --version")
    if result:
        print(f"✅ uv is installed: {result}")
        return True
    else:
        print("❌ uv is not installed")
        return False

def install_uv():
    """Install uv"""
    print("📦 Installing uv...")
    if os.name == 'nt':  # Windows
        cmd = 'powershell -c "irm https://astral.sh/uv/install.ps1 | iex"'
    else:  # Unix-like
        cmd = 'curl -LsSf https://astral.sh/uv/install.sh | sh'
    
    result = subprocess.run(cmd, shell=True)
    if result.returncode == 0:
        print("✅ uv installed successfully")
        return True
    else:
        print("❌ Failed to install uv")
        return False

def setup_project():
    """Set up the project with uv"""
    backend_dir = Path(__file__).parent.parent
    
    print("🔧 Setting up backend project with uv...")
    
    # Create virtual environment
    print("📦 Creating virtual environment...")
    if run_command("uv venv", cwd=backend_dir):
        print("✅ Virtual environment created")
    else:
        print("❌ Failed to create virtual environment")
        return False
    
    # Install dependencies
    print("📥 Installing dependencies...")
    if run_command("uv pip install -r requirements.txt", cwd=backend_dir):
        print("✅ Dependencies installed")
    else:
        print("❌ Failed to install dependencies")
        return False
    
    # Install development dependencies
    print("📥 Installing development dependencies...")
    if run_command("uv pip install -e .[dev]", cwd=backend_dir):
        print("✅ Development dependencies installed")
    else:
        print("⚠️ Failed to install development dependencies (optional)")
    
    # Check if .env file exists
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("⚙️ Creating .env file from template...")
        env_file.write_text(env_example.read_text())
        print("📝 Please edit .env file with your configuration")
    
    # Initialize database
    print("🗄️ Initializing database...")
    if run_command("uv run python init_db.py", cwd=backend_dir):
        print("✅ Database initialized")
    else:
        print("❌ Failed to initialize database")
        return False
    
    print("🎉 Backend setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your Twilio and ElevenLabs credentials")
    print("2. Run 'uv run python main.py' to start the server")
    print("3. Visit http://localhost:8000/docs for API documentation")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Backend Setup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        print("❌ Setup failed: Python 3.12+ is required")
        sys.exit(1)
    
    # Check if uv is installed
    if not check_uv_installed():
        if not install_uv():
            print("❌ Setup failed: Could not install uv")
            sys.exit(1)
    
    # Set up the project
    if not setup_project():
        print("❌ Setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
