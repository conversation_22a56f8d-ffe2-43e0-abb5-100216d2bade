import pytest
import asyncio
from app.stt.nova import NovaSTT
from app.config import settings


@pytest.mark.asyncio
@pytest.mark.skipif(not settings.nova_api_key, reason="NOVA_API_KEY is not set, skipping integration test.")
async def test_nova_real_transcription():
    """
    Tests the Nova STT service with a real connection to Deepgram.
    This is an integration test and requires a valid NOVA_API_KEY.
    """
    # Arrange
    audio_queue = asyncio.Queue()
    transcript_queue = asyncio.Queue()
    stt_service = NovaSTT()

    # Act
    transcribe_task = asyncio.create_task(stt_service.transcribe(audio_queue, transcript_queue))

    # Simulate sending a short, silent audio chunk to test the connection
    await audio_queue.put(b"\x00" * 3200)  # 0.1s of silence at 16kHz, 16-bit
    await audio_queue.put(None)

    # Assert
    try:
        # Wait for the end-of-transcription signal
        final_signal = await asyncio.wait_for(transcript_queue.get(), timeout=5)
        assert final_signal == (None, None)
    except asyncio.TimeoutError:
        pytest.fail("Timed out waiting for a response from the Nova service.")
    finally:
        # Ensure the task is cleaned up
        transcribe_task.cancel()
        try:
            await transcribe_task
        except asyncio.CancelledError:
            pass
