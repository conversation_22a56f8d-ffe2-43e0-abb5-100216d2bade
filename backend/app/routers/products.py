from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from .. import crud, models, schemas
from ..database import get_db

router = APIRouter(prefix="/products", tags=["products"])


@router.get("", response_model=List[schemas.Product])
def read_products(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = Query(None, description="Filter by product category"),
    search: Optional[str] = Query(
        None, description="Search products by name, description, or brand"
    ),
    db: Session = Depends(get_db),
):
    """
    Retrieve products with optional filtering and search
    """
    products = crud.get_products(
        db, skip=skip, limit=limit, category=category, search=search
    )
    return products


@router.post("", response_model=schemas.Product)
def create_product(product: schemas.ProductCreate, db: Session = Depends(get_db)):
    """
    Create a new product
    """
    # Check if SKU already exists (if provided)
    if product.sku:
        db_product = crud.get_product_by_sku(db, sku=product.sku)
        if db_product:
            raise HTTPException(status_code=400, detail="SKU already exists")

    return crud.create_product(db=db, product=product)


@router.get("/{product_id}", response_model=schemas.Product)
def read_product(product_id: int, db: Session = Depends(get_db)):
    """
    Get a specific product by ID
    """
    db_product = crud.get_product(db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="Product not found")
    return db_product


@router.put("/{product_id}", response_model=schemas.Product)
def update_product(
    product_id: int, product: schemas.ProductUpdate, db: Session = Depends(get_db)
):
    """
    Update a product's information
    """
    db_product = crud.get_product(db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="Product not found")

    # Check if SKU is being changed and if it already exists
    if product.sku and product.sku != db_product.sku:
        existing_product = crud.get_product_by_sku(db, sku=product.sku)
        if existing_product:
            raise HTTPException(status_code=400, detail="SKU already exists")

    return crud.update_product(db=db, product_id=product_id, product=product)


@router.delete("/{product_id}")
def delete_product(product_id: int, db: Session = Depends(get_db)):
    """
    Soft delete a product (mark as inactive)
    """
    db_product = crud.get_product(db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="Product not found")

    crud.delete_product(db=db, product_id=product_id)
    return {"message": "Product deleted successfully"}


@router.get("/categories/list")
def get_product_categories(db: Session = Depends(get_db)):
    """
    Get list of all product categories
    """
    categories = (
        db.query(models.Product.category)
        .filter(models.Product.category.isnot(None), models.Product.is_active == True)
        .distinct()
        .all()
    )

    return [category[0] for category in categories if category[0]]


@router.get("/search/suggestions")
def get_search_suggestions(
    query: str = Query(..., min_length=2, description="Search query"),
    limit: int = Query(10, le=20, description="Maximum number of suggestions"),
    db: Session = Depends(get_db),
):
    """
    Get search suggestions for products
    """
    products = crud.get_products(db, search=query, limit=limit)

    suggestions = []
    for product in products:
        suggestions.append(
            {
                "id": product.id,
                "name": product.name,
                "category": product.category,
                "price": product.price,
            }
        )

    return suggestions


@router.get("/{product_id}/enrichment", response_model=dict)
def get_product_enrichment(product_id: int, db: Session = Depends(get_db)):
    """
    Get product call enrichment data
    """
    db_product = crud.get_product(db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="Product not found")

    return db_product.enrichment or {}


@router.post("/{product_id}/enrichment", response_model=dict)
def update_product_enrichment(
    product_id: int, enrichment_data: dict, db: Session = Depends(get_db)
):
    """
    Update product call enrichment data
    """
    db_product = crud.get_product(db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="Product not found")

    # Update enrichment data
    updated_product = crud.update_product_enrichment(
        db=db, product_id=product_id, enrichment_data=enrichment_data
    )

    return updated_product.enrichment or {}
