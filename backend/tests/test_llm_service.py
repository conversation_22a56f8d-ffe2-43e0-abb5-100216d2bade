import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import os
import asyncio

# Add project root to path
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.llms.gemini import Gemini_LLM
from app.config import settings

# --- Test Case for Gemini LLM ---

@pytest.mark.asyncio
@patch('google.generativeai.GenerativeModel.generate_content_async')
async def test_gemini_chat(mock_generate_content):
    """
    Tests that the Gemini_LLM class correctly calls the API.
    """
    # 1. Setup
    settings.llm_provider = "google"
    llm = Gemini_LLM()
    
    # Mock the async generator
    async def mock_stream_generator():
        yield MagicMock(text="Test response")

    mock_generate_content.return_value = mock_stream_generator()

    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, world!"}
    ]

    # 2. Action
    response_generator = llm.chat(messages=messages)
    response_parts = [part async for part in response_generator]
    response = "".join(response_parts)

    # 3. Verification
    mock_generate_content.assert_called_once()
    
    # Verify the final response is correct
    assert response == "Test response"

    # Reset settings
    settings.llm_provider = os.getenv("LLM_PROVIDER", "google")
