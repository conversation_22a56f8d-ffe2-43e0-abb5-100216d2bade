import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { productAPI } from '../utils/api';
import toast from 'react-hot-toast';
import { X, Upload } from 'lucide-react';

export default function ProductModal({ product, onClose, onSave }) {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    stock_quantity: '',
    image_url: '',
    sizes: [],
    enrichment: {},
  });
  const [sizesInput, setSizesInput] = useState('');
  const [enrichmentInput, setEnrichmentInput] = useState('');

  useEffect(() => {
    if (product) {
      const enrichment = product.enrichment || {};
      setFormData({
        name: product.name || '',
        description: product.description || '',
        price: product.price || '',
        category: product.category || '',
        stock_quantity: product.stock_quantity || '',
        image_url: product.image_url || '',
        sizes: product.sizes || [],
        enrichment: enrichment,
      });
      setSizesInput((product.sizes || []).join(', '));
      setEnrichmentInput(JSON.stringify(enrichment, null, 2));
    } else {
      setFormData({
        name: '',
        description: '',
        price: '',
        category: '',
        stock_quantity: '',
        image_url: '',
        sizes: [],
        enrichment: {},
      });
      setSizesInput('');
      setEnrichmentInput('{}');
    }
  }, [product]);

  const createProductMutation = useMutation(productAPI.create, {
    onSuccess: () => {
      toast.success('Product created successfully!');
      queryClient.invalidateQueries('products');
      onSave();
    },
    onError: (error) => {
      toast.error(`Failed to create product: ${error.message}`);
    },
  });

  const updateProductMutation = useMutation(
    ({ id, data }) => productAPI.update(id, data),
    {
      onSuccess: () => {
        toast.success('Product updated successfully!');
        queryClient.invalidateQueries('products');
        onSave();
      },
      onError: (error) => {
        toast.error(`Failed to update product: ${error.message}`);
      },
    }
  );

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSizesChange = (e) => {
    setSizesInput(e.target.value);
    const sizesArray = e.target.value.split(',').map(s => s.trim()).filter(Boolean);
    setFormData(prev => ({ ...prev, sizes: sizesArray }));
  };

  const handleEnrichmentChange = (e) => {
    setEnrichmentInput(e.target.value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    let enrichmentData;
    try {
      enrichmentData = JSON.parse(enrichmentInput);
    } catch (err) {
      toast.error('Invalid JSON in enrichment field.');
      return;
    }

    const submissionData = {
      ...formData,
      price: parseFloat(formData.price),
      stock_quantity: parseInt(formData.stock_quantity, 10),
      enrichment: enrichmentData,
    };

    if (product) {
      updateProductMutation.mutate({ id: product.id, data: submissionData });
    } else {
      createProductMutation.mutate(submissionData);
    }
  };

  const isMutating = createProductMutation.isLoading || updateProductMutation.isLoading;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {product ? 'Edit Product' : 'Add New Product'}
                </h3>
                <button type="button" onClick={onClose} className="btn btn-ghost btn-sm p-1">
                  <X className="h-4 w-4" />
                </button>
              </div>

              <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Product Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="input mt-1 block w-full"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    name="description"
                    id="description"
                    rows="3"
                    value={formData.description}
                    onChange={handleChange}
                    className="textarea mt-1 block w-full"
                  ></textarea>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                      Price
                    </label>
                    <input
                      type="number"
                      name="price"
                      id="price"
                      step="0.01"
                      value={formData.price}
                      onChange={handleChange}
                      className="input mt-1 block w-full"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="stock_quantity" className="block text-sm font-medium text-gray-700">
                      Stock Quantity
                    </label>
                    <input
                      type="number"
                      name="stock_quantity"
                      id="stock_quantity"
                      value={formData.stock_quantity}
                      onChange={handleChange}
                      className="input mt-1 block w-full"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <input
                    type="text"
                    name="category"
                    id="category"
                    value={formData.category}
                    onChange={handleChange}
                    className="input mt-1 block w-full"
                  />
                </div>

                <div>
                  <label htmlFor="sizes" className="block text-sm font-medium text-gray-700">
                    Sizes (comma-separated)
                  </label>
                  <input
                    type="text"
                    name="sizes"
                    id="sizes"
                    value={sizesInput}
                    onChange={handleSizesChange}
                    className="input mt-1 block w-full"
                    placeholder="e.g. S, M, L, XL"
                  />
                </div>

                <div>
                  <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                    Image URL
                  </label>
                  <input
                    type="text"
                    name="image_url"
                    id="image_url"
                    value={formData.image_url}
                    onChange={handleChange}
                    className="input mt-1 block w-full"
                    placeholder="https://example.com/image.png"
                  />
                </div>

                <div>
                  <label htmlFor="enrichment" className="block text-sm font-medium text-gray-700">
                    Call Enrichment (JSON)
                  </label>
                  <textarea
                    name="enrichment"
                    id="enrichment"
                    rows="5"
                    value={enrichmentInput}
                    onChange={handleEnrichmentChange}
                    className="textarea mt-1 block w-full font-mono"
                    placeholder='Enter valid JSON for enrichment...'
                  ></textarea>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="btn btn-primary btn-md"
                disabled={isMutating}
              >
                {isMutating ? 'Saving...' : (product ? 'Save Changes' : 'Create Product')}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn btn-secondary btn-md mr-2"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
