import asyncio
import logging
from typing import As<PERSON><PERSON>enerator

from fastapi import APIRout<PERSON>, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from ..database import get_db
from ..services.conversation_service import ConversationManager
from ..stt import get_stt_service
from ..tts import get_tts_service
from ..websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/voice", tags=["voice_calls"])


@router.websocket("/web-call")
async def web_call_endpoint(websocket: WebSocket, db: Session = Depends(get_db)):
    await websocket.accept()
    logger.info("WebSocket connection accepted for web call.")

    init_message = await websocket.receive_json()
    logger.info(f"Received init message: {init_message}")

    manager = WebSocketManager(websocket, db)
    tts_service = get_tts_service()
    stt_service = get_stt_service()

    audio_queue = asyncio.Queue()
    raw_transcript_queue = asyncio.Queue()
    processed_transcript_queue = asyncio.Queue()
    interruption_detected = asyncio.Event()
    is_tts_active = asyncio.Event()

    async def audio_input_task():
        """Receives audio bytes from the client and puts them in a queue."""
        try:
            while True:
                data = await websocket.receive_bytes()
                await audio_queue.put(data)
        except WebSocketDisconnect:
            logger.info("Client disconnected from web call audio stream.")
            await audio_queue.put(None)
        except Exception as e:
            logger.error(f"Error in audio_input_task: {e}", exc_info=True)
            await audio_queue.put(None)

    async def transcript_distributor():
        """
        Distributes transcripts from the STT service.
        It also detects user interruptions while the agent is speaking.
        """
        while True:
            try:
                item = await raw_transcript_queue.get()
                if item is None:
                    await processed_transcript_queue.put(None)
                    break

                transcript, _ = item
                if is_tts_active.is_set() and transcript.strip() and not interruption_detected.is_set():
                    logger.info("User interruption detected.")
                    interruption_detected.set()

                await processed_transcript_queue.put(item)
            except asyncio.CancelledError:
                logger.info("Transcript distributor cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in transcript distributor: {e}", exc_info=True)
                break

    async def stt_input_handler(ws: WebSocket) -> str:
        """
        Pulls transcripts from the queue, detects user pauses,
        and returns the full transcript of an utterance.
        """
        full_transcript_parts = []
        pause_threshold = 0.8  # seconds

        while True:
            try:
                transcript, is_final = await asyncio.wait_for(processed_transcript_queue.get(), timeout=pause_threshold)

                if transcript is None:  # Stream ended
                    final_transcript = " ".join(full_transcript_parts).strip()
                    return final_transcript if final_transcript else None

                if is_final and transcript.strip():
                    full_transcript_parts.append(transcript)
                    current_utterance = " ".join(full_transcript_parts)
                    await websocket.send_json(
                        {
                            "type": "transcript",
                            "sender": "user",
                            "transcript": current_utterance,
                            "is_final": True,
                        }
                    )
                elif not is_final:  # interim
                    display_transcript = " ".join(full_transcript_parts)
                    if transcript.strip():
                        display_transcript += " " + transcript

                    await websocket.send_json(
                        {
                            "type": "transcript",
                            "sender": "user",
                            "transcript": display_transcript.strip(),
                            "is_final": False,
                        }
                    )

            except asyncio.TimeoutError:
                # Pause detected
                final_transcript = " ".join(full_transcript_parts).strip()
                if final_transcript:
                    logger.info(f"Pause detected. Sending full transcript: {final_transcript}")
                    return final_transcript
                # if no transcript yet, just continue waiting for speech

    async def streaming_tts_output_handler(
        text_chunk_generator: AsyncGenerator[str, None],
    ) -> str:
        """
        Receives a generator of text chunks, streams them to TTS, and handles interruptions.
        Returns the full text that was spoken or partially spoken.
        """
        is_tts_active.set()
        interruption_detected.clear()

        full_response = ""
        tts_stream_generator = None

        async def text_to_tts_generator():
            nonlocal full_response
            async for text_chunk in text_chunk_generator:
                full_response += text_chunk
                yield text_chunk

        try:
            tts_stream_generator = tts_service.stream_realtime(text_to_tts_generator(), interruption_detected)

            async for audio_chunk_b64 in tts_stream_generator:
                if interruption_detected.is_set():
                    logger.info("Interruption detected during TTS streaming.")
                    break

                await websocket.send_json({"type": "audio", "audio": audio_chunk_b64})

            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": full_response,
                    "is_final": True,
                }
            )
            return full_response

        except Exception as e:
            logger.error(f"Error in streaming TTS handler: {e}", exc_info=True)
            return full_response
        finally:
            is_tts_active.clear()
            if tts_stream_generator and hasattr(tts_stream_generator, "aclose"):
                await tts_stream_generator.aclose()
            await websocket.send_json({"type": "clear_audio_buffer"})
            logger.info("TTS output finished.")

    async def first_message_tts_handler(text: str):
        async def text_generator():
            yield text

        await streaming_tts_output_handler(text_generator())

    # Start the background tasks
    audio_receiver_task = asyncio.create_task(audio_input_task())
    stt_task = asyncio.create_task(stt_service.transcribe(audio_queue, raw_transcript_queue, sample_rate=16000))
    distributor_task = asyncio.create_task(transcript_distributor())

    try:
        data = init_message.get("data", init_message)
        call_history_id = data.get("call_history_id") or data.get("callHistoryId")

        if not call_history_id:
            raise ValueError("call_history_id not found in init message")

        await manager.conversation_manager.handle_conversation(
            call_history_id=call_history_id,
            input_handler=stt_input_handler,
            output_handler=streaming_tts_output_handler,
            first_message_handler=first_message_tts_handler,
            get_init_message=lambda: asyncio.sleep(0, result=init_message),
            websocket=websocket,
        )
    except Exception as e:
        logger.error(f"Error in web call endpoint: {e}", exc_info=True)
    finally:
        logger.info("Cancelling background tasks for web call.")
        audio_receiver_task.cancel()
        stt_task.cancel()
        distributor_task.cancel()
        try:
            await audio_receiver_task
        except asyncio.CancelledError:
            pass
        try:
            await stt_task
        except asyncio.CancelledError:
            pass
        try:
            await distributor_task
        except asyncio.CancelledError:
            pass

        if not websocket.client_state == "DISCONNECTED":
            try:
                await websocket.close()
            except RuntimeError as e:
                # This can happen if the client disconnects while the server is also closing
                logger.warning(f"Ignoring RuntimeError during websocket close: {e}")
        logger.info("Web call endpoint finished.")
