import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { useNavigate, Link } from 'react-router-dom';
import {
  ShoppingBag,
  Users,
  Phone,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  PhoneCall,
  Activity
} from 'lucide-react';
import { systemAPI, customerAPI, productAPI } from '../utils/api';
import { formatCurrency, formatDateTime } from '../utils/helpers';
import LoadingSpinner from '../components/LoadingSpinner';
import CustomerModal from '../components/CustomerModal';
import ProductModal from '../components/ProductModal';
import LiveTranscriptDashboard from '../components/LiveTranscriptDashboard';
import StatsCard from '../components/StatsCard';
import RecentActivity from '../components/RecentActivity';
import WebCallWidget from '../components/WebCallWidget';

export default function Dashboard() {
  const navigate = useNavigate();
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showWebCall, setShowWebCall] = useState(false);

  // Fetch system status
  const { data: systemStatus, isLoading: statusLoading } = useQuery(
    'system-status',
    systemAPI.getStatus,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch recent customers
  const { data: customersData, isLoading: customersLoading, refetch: refetchCustomers } = useQuery(
    'recent-customers',
    () => customerAPI.getAll({ limit: 5 }),
    {
      select: (response) => response.data ?? [],
    }
  );

  // Fetch recent products
  const { data: productsData, isLoading: productsLoading, refetch: refetchProducts } = useQuery(
    'recent-products',
    () => productAPI.getAll({ limit: 5 }),
    {
      select: (response) => response.data ?? [],
    }
  );

  // Fetch recent calls
  const { data: callHistoryData, isLoading: callHistoryLoading } = useQuery(
    'recent-calls',
    () => customerAPI.getAll({ limit: 1 }).then(res => {
      if (res.data && res.data.length > 0) {
        return customerAPI.getCallHistory(res.data[0].id);
      }
      return { data: [] };
    }),
    {
      select: (response) => response.data ?? [],
    }
  );

  // Quick action handlers
  const handleAddCustomer = () => {
    setShowCustomerModal(true);
  };

  const handleAddProduct = () => {
    setShowProductModal(true);
  };

  const handleMakeCall = () => {
    navigate('/call-center');
  };

  const handleCustomerSaved = () => {
    setShowCustomerModal(false);
    refetchCustomers();
  };

  const handleProductSaved = () => {
    setShowProductModal(false);
    refetchProducts();
  };

  if (statusLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const stats = [
    {
      name: 'Total Customers',
      value: systemStatus?.data?.customers || 0,
      icon: Users,
      change: '+12%',
      changeType: 'increase',
      color: 'blue',
    },
    {
      name: 'Total Products',
      value: systemStatus?.data?.products || 0,
      icon: ShoppingBag,
      change: '+5%',
      changeType: 'increase',
      color: 'green',
    },
    {
      name: 'Active Calls',
      value: 0, // This would come from real-time data
      icon: Phone,
      change: '0%',
      changeType: 'neutral',
      color: 'purple',
    },
    {
      name: 'Revenue Today',
      value: formatCurrency(0), // This would come from orders data
      icon: DollarSign,
      change: '+8%',
      changeType: 'increase',
      color: 'yellow',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome to your e-commerce and call center dashboard
        </p>
      </div>

      {/* System Status Alert */}
      {systemStatus?.data && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <Activity className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-sm font-medium text-gray-900">System Status</span>
          </div>
          <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Database:</span>
              <span className={`ml-2 font-medium ${systemStatus.data.database === 'connected' ? 'text-green-600' : 'text-red-600'
                }`}>
                {systemStatus.data.database}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Twilio:</span>
              <span className={`ml-2 font-medium ${systemStatus.data.twilio_configured ? 'text-green-600' : 'text-yellow-600'
                }`}>
                {systemStatus.data.twilio_configured ? 'Configured' : 'Not Configured'}
              </span>
            </div>
            <div>
              <span className="text-gray-500">ElevenLabs:</span>
              <span className={`ml-2 font-medium ${systemStatus.data.elevenlabs_configured ? 'text-green-600' : 'text-yellow-600'
                }`}>
                {systemStatus.data.elevenlabs_configured ? 'Configured' : 'Not Configured'}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Status:</span>
              <span className="ml-2 font-medium text-green-600">
                {systemStatus.data.status}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <StatsCard key={stat.name} {...stat} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Customers */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Recent Customers</h3>
              <Users className="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <div className="p-6">
            {customersLoading ? (
              <div className="flex justify-center">
                <LoadingSpinner />
              </div>
            ) : customersData && customersData.length > 0 ? (
              <div className="space-y-4">
                {customersData.map((customer) => (
                  <Link
                    key={customer.id}
                    to={`/customers/${customer.id}`}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center group-hover:bg-primary-200">
                        <span className="text-sm font-medium text-primary-600">
                          {customer.name.charAt(0)}
                        </span>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900 group-hover:text-primary-900">{customer.name}</p>
                        <p className="text-sm text-gray-500">{customer.email}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">
                        {formatDateTime(customer.created_at)}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center">No customers found</p>
            )}
          </div>
        </div>

        {/* Recent Products */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Recent Products</h3>
              <ShoppingBag className="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <div className="p-6">
            {productsLoading ? (
              <div className="flex justify-center">
                <LoadingSpinner />
              </div>
            ) : productsData && productsData.length > 0 ? (
              <div className="space-y-4">
                {productsData.map((product) => (
                  <Link
                    key={product.id}
                    to={`/products/${product.id}`}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center group-hover:bg-green-100">
                        <ShoppingBag className="h-5 w-5 text-gray-400 group-hover:text-green-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900 group-hover:text-green-900">{product.name}</p>
                        <p className="text-sm text-gray-500">{product.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(product.price)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Stock: {product.stock_quantity}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center">No products found</p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
          <button
            onClick={handleAddCustomer}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-primary-300 transition-colors group"
          >
            <Users className="h-8 w-8 text-primary-600 mr-3 group-hover:text-primary-700" />
            <div className="text-left">
              <p className="font-medium text-gray-900 group-hover:text-primary-900">Add Customer</p>
              <p className="text-sm text-gray-500">Create a new customer profile</p>
            </div>
          </button>
          <button
            onClick={handleAddProduct}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-green-300 transition-colors group"
          >
            <ShoppingBag className="h-8 w-8 text-green-600 mr-3 group-hover:text-green-700" />
            <div className="text-left">
              <p className="font-medium text-gray-900 group-hover:text-green-900">Add Product</p>
              <p className="text-sm text-gray-500">Add a new product to catalog</p>
            </div>
          </button>
          <button
            onClick={handleMakeCall}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-purple-300 transition-colors group"
          >
            <PhoneCall className="h-8 w-8 text-purple-600 mr-3 group-hover:text-purple-700" />
            <div className="text-left">
              <p className="font-medium text-gray-900 group-hover:text-purple-900">Make Call</p>
              <p className="text-sm text-gray-500">Start a voice call session</p>
            </div>
          </button>
          <button
            onClick={() => setShowWebCall(true)}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-colors group"
          >
            <Phone className="h-8 w-8 text-blue-600 mr-3 group-hover:text-blue-700" />
            <div className="text-left">
              <p className="font-medium text-gray-900 group-hover:text-blue-900">Talk to Assistant</p>
              <p className="text-sm text-gray-500">Get help from the AI assistant</p>
            </div>
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        {callHistoryLoading ? (
          <LoadingSpinner />
        ) : (
          <RecentActivity
            activities={callHistoryData.map(call => ({
              icon: PhoneCall,
              description: `Call with ${call.customer?.name || 'Unknown'}`,
              timestamp: call.created_at,
              link: `/calls/${call.id}`,
            }))}
          />
        )}
      </div>

      {/* Modals */}
      {showCustomerModal && (
        <CustomerModal
          onClose={() => setShowCustomerModal(false)}
          onSave={handleCustomerSaved}
        />
      )}

      {showProductModal && (
        <ProductModal
          onClose={() => setShowProductModal(false)}
          onSave={handleProductSaved}
        />
      )}

      {showWebCall && (
        <WebCallWidget
          onClose={() => setShowWebCall(false)}
        />
      )}
    </div>
  );
}
