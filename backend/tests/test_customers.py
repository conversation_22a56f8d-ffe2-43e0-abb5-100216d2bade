from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app import models

def test_create_customer(client: TestClient):
    response = client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["name"] == "Test User"
    assert "id" in data

def test_create_customer_duplicate_phone(client: TestClient, db_session: Session):
    client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    response = client.post(
        "/api/customers",
        json={"name": "Another User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    assert response.status_code == 400
    assert response.json() == {"detail": "Phone number already registered"}

def test_read_customers(client: TestClient, db_session: Session):
    client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    response = client.get("/api/customers")
    assert response.status_code == 200
    data = response.json()
    assert len(data) > 0
    assert data[0]["name"] == "Test User"

def test_read_customer(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    customer_id = post_response.json()["id"]
    response = client.get(f"/api/customers/{customer_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test User"
    assert data["id"] == customer_id

def test_read_customer_not_found(client: TestClient):
    response = client.get("/api/customers/999")
    assert response.status_code == 404
    assert response.json() == {"detail": "Customer not found"}

def test_update_customer(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    customer_id = post_response.json()["id"]
    response = client.put(
        f"/api/customers/{customer_id}",
        json={"name": "Updated"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated"
    assert data["id"] == customer_id

def test_delete_customer(client: TestClient, db_session: Session):
    post_response = client.post(
        "/api/customers",
        json={"name": "Test User", "phone": "1234567890", "email": "<EMAIL>"},
    )
    customer_id = post_response.json()["id"]
    response = client.delete(f"/api/customers/{customer_id}")
    assert response.status_code == 200
    assert response.json() == {"message": "Customer deleted successfully"}

    # Verify the customer is marked as inactive
    db_customer = db_session.query(models.Customer).filter(models.Customer.id == customer_id).first()
    assert db_customer.is_active is False
