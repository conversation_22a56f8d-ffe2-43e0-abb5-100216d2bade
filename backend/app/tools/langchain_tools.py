import logging
from typing import Annotated

from langchain.tools import tool
from sqlalchemy import and_

from .. import crud, models, schemas
from ..database import get_db
from ..utils.helpers import model_to_dict

logger = logging.getLogger(__name__)


@tool
def get_cart_summary(customer_id: int):
    """Get a summary of the customer's active shopping cart."""
    db = next(get_db())
    logger.info(f"Langchain Tool: get_cart_summary for customer_id: {customer_id}")
    return crud.get_cart_summary(db, customer_id)


@tool
def search_products(query: str = None, category: str = None):
    """Search for products using a query or category, returning a summarized result."""
    db = next(get_db())
    logger.info(
        f"Langchain Tool: search_products with query: {query}, category: {category}"
    )
    products = crud.get_products(db, search=query, category=category)
    return [model_to_dict(p) for p in products]


@tool
def add_cart_item_webhook(
    customer_id: int,
    product_name: str,
    quantity: int,
    size: str = None,
    color: str = None,
):
    """Add an item to the customer's cart using product name."""
    db = next(get_db())
    logger.info(
        f"Langchain Tool: add_cart_item_webhook for customer_id: {customer_id}, product_name: {product_name}, quantity: {quantity}"
    )
    customer_cart = crud.get_customer_active_cart(db, customer_id)
    if not customer_cart:
        customer_cart = crud.create_cart(
            db, schemas.CartCreate(customer_id=customer_id)
        )

    product_list = crud.get_products(db, search=product_name)
    if not product_list:
        return {"error": "Product not found."}
    product = product_list[0]

    cart_item_schema = schemas.CartItemCreate(
        product_id=product.id, quantity=quantity, size=size, color=color
    )
    cart_item = crud.add_cart_item(db, customer_cart.id, cart_item_schema)
    return (
        model_to_dict(cart_item)
        if cart_item
        else {"error": "Failed to add item to cart."}
    )


@tool
def remove_cart_item(customer_id: int, product_name: str, quantity: int):
    """Remove an item from the customer's cart using product name."""
    db = next(get_db())
    logger.info(
        f"Langchain Tool: remove_cart_item for customer_id: {customer_id}, product_name: {product_name}, quantity: {quantity}"
    )
    customer_cart = crud.get_customer_active_cart(db, customer_id)
    if not customer_cart:
        return {"error": "Active cart not found for customer."}

    product_list = crud.get_products(db, search=product_name)
    if not product_list:
        return {"error": "Product not found."}
    product = product_list[0]

    existing_item = (
        db.query(models.CartItem)
        .filter(
            and_(
                models.CartItem.cart_id == customer_cart.id,
                models.CartItem.product_id == product.id,
            )
        )
        .first()
    )

    if not existing_item:
        return {"error": "Item not found in cart."}

    if quantity >= existing_item.quantity:
        removed_item = crud.remove_cart_item(db, existing_item.id)
        return {"message": f"Removed all {product.name} from the cart."}
    else:
        existing_item.quantity -= quantity
        db.commit()
        db.refresh(existing_item)
        return model_to_dict(existing_item)


@tool
def checkout_cart(customer_id: int):
    """Process the customer's active cart checkout."""
    db = next(get_db())
    logger.info(f"Langchain Tool: checkout_cart for customer_id: {customer_id}")
    customer_cart = crud.get_customer_active_cart(db, customer_id)
    if not customer_cart or not customer_cart.items:
        return {"error": "Cart is empty, nothing to checkout."}

    order_items = []
    for item in customer_cart.items:
        order_items.append(
            {
                "product_id": item.product_id,
                "quantity": item.quantity,
                "price": item.price,
                "size": item.size,
                "color": item.color,
            }
        )

    order_schema = schemas.OrderCreate(
        customer_id=customer_id,
        total_amount=customer_cart.total_amount,
        status="completed",
        items=order_items,
    )
    crud.create_order(db, order_schema)

    db_cart = db.query(models.Cart).filter(models.Cart.id == customer_cart.id).first()
    if db_cart:
        db_cart.status = "completed"
        db.commit()
        db.refresh(db_cart)
        return model_to_dict(db_cart)
    return {"error": "Failed to checkout cart."}


@tool
def get_customer_by_id(customer_id: int):
    """Get details for a specific customer by their ID."""
    db = next(get_db())
    logger.info(f"Langchain Tool: get_customer_by_id for customer_id: {customer_id}")
    customer = crud.get_customer(db, customer_id)
    return model_to_dict(customer) if customer else {"error": "Customer not found."}


@tool
def get_product_categories():
    """Get a list of all product categories."""
    db = next(get_db())
    logger.info("Langchain Tool: get_product_categories")
    categories = db.query(models.Product.category).distinct().all()
    return [c[0] for c in categories]


@tool
def get_search_suggestions(query: str):
    """Get search suggestions for a given query.
    Args:
        query (str): The search query.
    """
    logger.info(f"Langchain Tool: get_search_suggestions for query: {query}")
    # This is a mock implementation. In a real application, this would
    # be a more sophisticated suggestion engine.
    return [f"suggestion for {query} 1", f"suggestion for {query} 2"]
