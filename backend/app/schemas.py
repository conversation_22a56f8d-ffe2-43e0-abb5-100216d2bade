from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr


# Customer Schemas
class CustomerBase(BaseModel):
    name: str
    phone: str
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: str = "US"
    notes: Optional[str] = None
    enrichment: Optional[Dict[str, Any]] = None


class CustomerCreate(CustomerBase):
    pass


class CustomerUpdate(CustomerBase):
    name: Optional[str] = None
    phone: Optional[str] = None


class Customer(CustomerBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Product Schemas
class ProductBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    category: Optional[str] = None
    brand: Optional[str] = None
    sku: Optional[str] = None
    sizes: Optional[List[str]] = None
    colors: Optional[List[str]] = None
    image_url: Optional[str] = None
    additional_images: Optional[List[str]] = None
    stock_quantity: int = 0
    weight: Optional[float] = None
    dimensions: Optional[Dict[str, float]] = None
    tags: Optional[List[str]] = None
    enrichment: Optional[Dict[str, Any]] = None


class ProductCreate(ProductBase):
    pass


class ProductUpdate(ProductBase):
    name: Optional[str] = None
    price: Optional[float] = None


class Product(ProductBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Cart Schemas
class CartItemBase(BaseModel):
    product_id: int
    quantity: int = 1
    size: Optional[str] = None
    color: Optional[str] = None


class CartItemCreate(CartItemBase):
    pass


class CartItemUpdate(BaseModel):
    quantity: Optional[int] = None
    size: Optional[str] = None
    color: Optional[str] = None


class CartItem(CartItemBase):
    id: int
    cart_id: int
    price: float
    added_at: datetime
    product: Product

    class Config:
        from_attributes = True


class CartBase(BaseModel):
    customer_id: int
    status: str = "active"


class CartCreate(CartBase):
    pass


class Cart(CartBase):
    id: int
    total_amount: float
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[CartItem] = []

    class Config:
        from_attributes = True


# Call History Schemas
class CallHistoryBase(BaseModel):
    customer_id: int
    phone_number: Optional[str] = None
    call_type: str = "outbound"
    call_metadata: Optional[Dict[str, Any]] = {"platform": "phone"}


class CallHistoryCreate(CallHistoryBase):
    pass


class CallHistory(CallHistoryBase):
    id: int
    call_sid: Optional[str] = None
    status: Optional[str] = None
    call_duration: Optional[int] = None
    system_prompt: Optional[str] = None
    first_message: Optional[str] = None
    conversation_log: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    customer: Optional[Customer] = None

    class Config:
        from_attributes = True


class CallHistoryUpdate(BaseModel):
    conversation_log: Optional[List[Dict[str, Any]]] = None
    call_duration: Optional[int] = None
    status: Optional[str] = None


class ConversationLogEvent(BaseModel):
    event_type: str  # e.g., "llm_call", "transcript", "tool_call", "tool_result", "user_input", "agent_output"
    timestamp: datetime
    data: Dict[str, Any]
    source: str  # e.g., "phone_stream", "web_call", "chat"


# Voice Call Schemas
class OutboundCallRequest(BaseModel):
    customer_id: int
    phone_number: str
    custom_prompt: Optional[str] = None
    custom_first_message: Optional[str] = None
    webhook_data: Optional[Dict[str, Any]] = None


class VoiceCallResponse(BaseModel):
    success: bool
    message: str
    call_sid: Optional[str] = None
    call_history_id: Optional[int] = None


class OutboundWebCallRequest(BaseModel):
    customer_id: int
    custom_prompt: Optional[str] = None
    custom_first_message: Optional[str] = None


class WebCallResponse(BaseModel):
    success: bool
    message: str
    call_history_id: Optional[int] = None


# Webhook Schemas
class CartSummaryRequest(BaseModel):
    customer_id: int


class CartSummaryResponse(BaseModel):
    success: bool
    cart_summary: str
    items: List[Dict[str, Any]]
    total: float


class ProductSearchRequest(BaseModel):
    query: Optional[str] = None
    category: Optional[str] = None


class ProductSearchResponse(BaseModel):
    success: bool
    search_results: str
    products: List[Dict[str, Any]]


class AddCartItemRequest(BaseModel):
    customer_id: int
    product_name: str
    quantity: int = 1
    size: Optional[str] = None
    color: Optional[str] = None


class RemoveCartItemRequest(BaseModel):
    customer_id: int
    product_name: str
    quantity: int = 1


class CheckoutCartRequest(BaseModel):
    customer_id: int


class WebhookResponse(BaseModel):
    success: bool
    message: str


# Prompt Preview Schemas
class PromptPreviewRequest(BaseModel):
    custom_prompt: Optional[str] = None
    custom_first_message: Optional[str] = None
    webhook_data: Optional[Dict[str, Any]] = None


class PromptPreviewResponse(BaseModel):
    system_prompt: str
    first_message: str


# Order Schemas
class OrderItemCreate(BaseModel):
    product_id: int
    quantity: int
    price: float
    size: Optional[str] = None
    color: Optional[str] = None


class OrderCreate(BaseModel):
    customer_id: int
    total_amount: float
    status: str
    items: List[OrderItemCreate]


class OrderItem(OrderItemCreate):
    id: int
    order_id: int
    product: Product

    class Config:
        from_attributes = True


class Order(BaseModel):
    id: int
    customer_id: int
    total_amount: float
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[OrderItem] = []

    class Config:
        from_attributes = True


# Performance Metric Schemas
class PerformanceMetricBase(BaseModel):
    service: str
    action: str
    latency: float


class PerformanceMetricCreate(PerformanceMetricBase):
    pass


class PerformanceMetric(PerformanceMetricBase):
    id: int
    timestamp: datetime

    class Config:
        from_attributes = True
