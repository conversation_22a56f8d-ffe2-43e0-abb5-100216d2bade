#!/usr/bin/env python3
"""
Example of how to integrate realtime TTS with an LLM for conversational AI.
This demonstrates the key benefit: audio starts playing while the LLM is still generating text.
"""

import asyncio
import logging
import os
import sys
from typing import AsyncGenerator

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import settings
from app.tts.factory import get_tts_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def simulate_llm_streaming(prompt: str) -> AsyncGenerator[str, None]:
    """
    Simulates an LLM generating text in real-time.
    In a real application, this would be replaced with actual LLM streaming.
    """
    # Simulate a response to the prompt
    if "weather" in prompt.lower():
        response = "The weather today is quite pleasant with partly cloudy skies and a temperature of around 72 degrees Fahrenheit. There's a gentle breeze coming from the west, making it perfect for outdoor activities. You might want to bring a light jacket if you're planning to stay out after sunset, as temperatures are expected to drop to around 65 degrees."
    elif "recipe" in prompt.lower():
        response = "Here's a simple recipe for chocolate chip cookies. First, preheat your oven to 375 degrees Fahrenheit. In a large bowl, cream together one cup of butter with three-quarters cup of brown sugar and one-quarter cup of white sugar until light and fluffy. Beat in two eggs one at a time, then add two teaspoons of vanilla extract. In a separate bowl, whisk together two and one-quarter cups of all-purpose flour, one teaspoon of baking soda, and one teaspoon of salt. Gradually mix the dry ingredients into the wet ingredients until just combined. Fold in two cups of chocolate chips. Drop rounded tablespoons of dough onto ungreased baking sheets and bake for 9 to 11 minutes until golden brown."
    else:
        response = "I understand your question and I'm happy to help you with that. Let me provide you with a comprehensive answer that covers all the important aspects of your inquiry. This is a demonstration of how realtime text-to-speech can provide immediate audio feedback while the language model is still generating the complete response."
    
    # Split into words and yield them in chunks to simulate streaming
    words = response.split()
    chunk_size = 3  # Yield 3 words at a time
    
    for i in range(0, len(words), chunk_size):
        chunk = " ".join(words[i:i + chunk_size]) + " "
        logger.info(f"LLM generated: '{chunk.strip()}'")
        yield chunk
        # Simulate the time it takes for LLM to generate next chunk
        await asyncio.sleep(0.3)


async def conversational_ai_with_realtime_tts(user_input: str):
    """
    Demonstrates a conversational AI system with realtime TTS.
    Audio starts playing immediately as the LLM generates text.
    """
    logger.info(f"User input: '{user_input}'")
    
    # Get the configured TTS service
    tts_service = get_tts_service()
    interruption_event = asyncio.Event()
    
    try:
        # Start LLM text generation
        logger.info("Starting LLM text generation...")
        text_stream = simulate_llm_streaming(user_input)
        
        # Start realtime TTS streaming
        logger.info("Starting realtime TTS streaming...")
        audio_chunks = []
        
        # Use realtime streaming if available, otherwise fall back to regular streaming
        if hasattr(tts_service, 'stream_realtime'):
            audio_stream = tts_service.stream_realtime(text_stream, interruption_event)
        else:
            logger.info("Realtime streaming not available, using regular streaming")
            audio_stream = tts_service.stream(text_stream, interruption_event)
        
        # Process audio chunks as they arrive
        chunk_count = 0
        async for audio_chunk in audio_stream:
            chunk_count += 1
            audio_chunks.append(audio_chunk)
            logger.info(f"Received audio chunk #{chunk_count} (length: {len(audio_chunk)})")
            
            # In a real application, you would send these chunks to the audio player
            # For this demo, we'll just collect them
            
            # Simulate some processing time
            await asyncio.sleep(0.1)
        
        logger.info(f"Conversation completed! Generated {len(audio_chunks)} audio chunks.")
        
        # In a real application, you might want to save the complete audio
        # or continue the conversation loop
        
    except Exception as e:
        logger.error(f"Error in conversational AI: {e}", exc_info=True)
    finally:
        # Clean up
        interruption_event.set()


async def demo_interruption_handling():
    """
    Demonstrates how to handle interruptions in realtime TTS.
    This is useful when users want to interrupt the AI response.
    """
    logger.info("Demonstrating interruption handling...")
    
    tts_service = get_tts_service()
    interruption_event = asyncio.Event()
    
    try:
        # Start a long response
        long_text = "This is a very long response that would normally take a while to complete. " * 10
        
        async def long_text_generator():
            words = long_text.split()
            for word in words:
                yield word + " "
                await asyncio.sleep(0.2)  # Slow generation
        
        # Start TTS streaming
        if hasattr(tts_service, 'stream_realtime'):
            audio_stream = tts_service.stream_realtime(long_text_generator(), interruption_event)
        else:
            audio_stream = tts_service.stream(long_text_generator(), interruption_event)
        
        # Simulate user interruption after a few chunks
        chunk_count = 0
        async for audio_chunk in audio_stream:
            chunk_count += 1
            logger.info(f"Received audio chunk #{chunk_count}")
            
            # Simulate user interruption after 3 chunks
            if chunk_count >= 3:
                logger.info("User interrupted! Stopping TTS...")
                interruption_event.set()
                break
        
        logger.info("Interruption handling demo completed.")
        
    except Exception as e:
        logger.error(f"Error in interruption demo: {e}", exc_info=True)


async def main():
    """Main demo function."""
    logger.info("Starting realtime TTS integration demo...")
    
    # Demo 1: Basic conversational AI with realtime TTS
    logger.info("\n=== Demo 1: Conversational AI with Realtime TTS ===")
    await conversational_ai_with_realtime_tts("What's the weather like today?")
    
    await asyncio.sleep(2)
    
    # Demo 2: Another conversation
    logger.info("\n=== Demo 2: Recipe Request ===")
    await conversational_ai_with_realtime_tts("Can you give me a recipe for chocolate chip cookies?")
    
    await asyncio.sleep(2)
    
    # Demo 3: Interruption handling
    logger.info("\n=== Demo 3: Interruption Handling ===")
    await demo_interruption_handling()
    
    logger.info("\nAll demos completed!")


if __name__ == "__main__":
    # Check if TTS is configured
    if settings.tts_provider == "local":
        logger.info("Using local TTS (espeak)")
    elif settings.tts_provider == "elevenlabs":
        if not settings.elevenlabs_api_key:
            logger.error("ElevenLabs API key not configured!")
            sys.exit(1)
        logger.info("Using ElevenLabs TTS")
    elif settings.tts_provider == "nova":
        if not settings.nova_api_key:
            logger.error("Nova API key not configured!")
            sys.exit(1)
        logger.info("Using Nova (Deepgram) TTS")
    
    asyncio.run(main())
