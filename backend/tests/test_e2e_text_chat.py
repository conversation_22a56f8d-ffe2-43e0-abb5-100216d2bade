import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import time

from app.app import app
from app.database import get_db, Base
from app import crud, schemas
from tests.database import TestingSessionLocal, engine

# The user's side of the conversation to drive the test
user_prompts = [
    "what is in my cart?",
    "can you remove one blue denim jeans?",
    "thats it, thanks.",
    "yes"
]

@pytest.fixture(scope="function")
def db_session():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    crud.create_product(db, schemas.ProductCreate(name="Classic White T-Shirt", price=29.99, description="A timeless classic."))
    crud.create_product(db, schemas.ProductCreate(name="Blue Denim Jeans", price=79.99, description="Perfect for any occasion."))
    db.commit()
    yield db
    db.close()
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    app.dependency_overrides[get_db] = lambda: db_session
    return TestClient(app)

def test_text_chat_e2e_conversation_real_llm(client, db_session: Session):
    # 1. Setup customer and cart
    customer = crud.create_customer(db_session, schemas.CustomerCreate(name="John Smith", email="<EMAIL>", phone="1234567890"))
    tshirt = crud.get_product_by_name(db_session, "Classic White T-Shirt")
    jeans = crud.get_product_by_name(db_session, "Blue Denim Jeans")
    
    cart = crud.get_customer_active_cart(db_session, customer.id)
    crud.add_cart_item(db_session, cart.id, schemas.CartItemCreate(product_id=tshirt.id, quantity=1, size="XS", color="White"))
    crud.add_cart_item(db_session, cart.id, schemas.CartItemCreate(product_id=jeans.id, quantity=2, size="28", color="Blue"))
    
    call_history = crud.create_call_history(db_session, schemas.CallHistoryCreate(
        customer_id=customer.id,
        call_type="text",
        system_prompt="You are Ava, a helpful shopping assistant. Your goal is to help the user with their cart and guide them to checkout.",
        first_message="Hi {customer_name}! This is Ava, how can I help you with your cart?"
    ))
    db_session.commit()

    # 2. Simulate the WebSocket conversation with the real LLM
    with client.websocket_connect(f"/api/voice/text-chat-ws") as websocket:
        websocket.send_json({
            "type": "init",
            "call_history_id": call_history.id
        })

        # Receive and print the first message
        response = websocket.receive_json()
        print(f"Agent: {response['transcript']}")
        assert "John Smith" in response['transcript']

        # Go through the user prompts
        for user_prompt in user_prompts:
            print(f"User: {user_prompt}")
            websocket.send_json({"type": "user_message", "text": user_prompt})
            
            while True:
                agent_response = websocket.receive_json()
                if agent_response["type"] == "tool_call":
                    print(f"Agent tool call: {agent_response}")
                elif agent_response["type"] == "transcript":
                    print(f"Agent: {agent_response['transcript']}")
                    break
    
    # 3. Assert the final state of the database
    db_session.refresh(cart)
    jeans_item = next((item for item in cart.items if item.product.name == "Blue Denim Jeans"), None)
    assert jeans_item is not None
    assert jeans_item.quantity == 1
    
    assert not cart.is_active
