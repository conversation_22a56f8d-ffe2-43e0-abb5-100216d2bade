import React from 'react';
import { X } from 'lucide-react';

export default function OrderHistoryModal({ isOpen, onClose, order }) {
  if (!isOpen || !order) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Order Details</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <X size={24} />
          </button>
        </div>
        <div>
          <p><strong>Order ID:</strong> {order.id}</p>
          <p><strong>Status:</strong> {order.status}</p>
          <p><strong>Total:</strong> ${order.total_amount.toFixed(2)}</p>
          <p><strong>Date:</strong> {new Date(order.created_at).toLocaleString()}</p>
          <h3 className="text-lg font-semibold mt-4">Items:</h3>
          <ul>
            {order.items.map(item => (
              <li key={item.id} className="border-t mt-2 pt-2">
                <p>{item.product.name} - ${item.price.toFixed(2)}</p>
                <p>Quantity: {item.quantity}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
