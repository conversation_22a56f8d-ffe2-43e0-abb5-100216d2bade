"""Add PromptTest table

Revision ID: 7059a802f81f
Revises: daf3dbce30cd
Create Date: 2025-07-29 02:06:08.804667

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7059a802f81f'
down_revision: Union[str, Sequence[str], None] = 'daf3dbce30cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('prompt_tests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('system_prompt', sa.Text(), nullable=False),
    sa.Column('first_message', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_prompt_tests_id'), 'prompt_tests', ['id'], unique=False)
    op.create_index(op.f('ix_prompt_tests_name'), 'prompt_tests', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_prompt_tests_name'), table_name='prompt_tests')
    op.drop_index(op.f('ix_prompt_tests_id'), table_name='prompt_tests')
    op.drop_table('prompt_tests')
    # ### end Alembic commands ###
