from ..config import settings
from .assemblyai import AssemblyAI_STT
from .base import STTBase as STT
from .local import LocalSTT
from .nova import Nova_STT


def get_stt_service() -> STT:
    stt_provider = settings.stt_provider.lower()
    if stt_provider == "local":
        return LocalSTT()
    elif stt_provider == "assemblyai":
        return AssemblyAI_STT()
    elif stt_provider == "nova":
        return Nova_STT()
    else:
        raise ValueError(f"Unsupported STT provider: {stt_provider}")
