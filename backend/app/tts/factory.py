from ..config import settings
from .base import TTSBase as TTS
from .elevenlabs import ElevenLabsTTS
from .local import LocalTTS
from .nova import NovaTTS


def get_tts_service() -> TTS:
    tts_provider = settings.tts_provider.lower()
    if tts_provider == "local":
        return LocalTTS()
    elif tts_provider == "elevenlabs":
        return ElevenLabsTTS()
    elif tts_provider == "nova":
        return NovaTTS()
    else:
        raise ValueError(f"Unsupported TTS provider: {tts_provider}")
