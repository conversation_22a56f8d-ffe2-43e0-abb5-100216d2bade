import logging

from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from ..global_websocket_manager import manager

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/ws", tags=["frontend_ws"])


@router.websocket("/call-updates/{call_history_id}")
async def websocket_endpoint(websocket: WebSocket, call_history_id: int):
    await manager.connect(websocket, call_history_id)
    try:
        while True:
            # Keep the connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, call_history_id)
