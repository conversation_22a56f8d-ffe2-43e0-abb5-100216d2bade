from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

def test_get_cart_summary_webhook(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "Webhook User", "phone": "4445556666", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    product_response = client.post(
        "/api/products",
        json={"name": "Webhook Product", "price": 25.0},
    )
    product_id = product_response.json()["id"]
    client.post(
        f"/api/customers/{customer_id}/cart/items",
        json={"product_id": product_id, "quantity": 2},
    )

    response = client.post(
        "/api/webhooks/get-cart-summary",
        json={"customer_id": customer_id},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "You have 1 item in your cart" in data["cart_summary"]
    assert data["total"] == 50.0

def test_search_products_webhook(client: TestClient, db_session: Session):
    client.post(
        "/api/products",
        json={"name": "Searchable Product", "price": 10.0},
    )
    response = client.post(
        "/api/webhooks/search-products",
        json={"query": "Searchable"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Found 1 product" in data["search_results"]
    assert len(data["products"]) == 1
    assert data["products"][0]["name"] == "Searchable Product"

def test_add_cart_item_webhook(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "Webhook User", "phone": "4445556666", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    product_response = client.post(
        "/api/products",
        json={"name": "Webhook Product", "price": 25.0},
    )
    product_id = product_response.json()["id"]

    response = client.post(
        "/api/webhooks/add-cart-item",
        json={"customer_id": customer_id, "product_name": "Webhook Product", "quantity": 1},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Added 1x Webhook Product" in data["message"]

def test_remove_cart_item_webhook(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "Webhook User", "phone": "4445556666", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    product_response = client.post(
        "/api/products",
        json={"name": "Webhook Product", "price": 25.0},
    )
    product_id = product_response.json()["id"]
    client.post(
        f"/api/customers/{customer_id}/cart/items",
        json={"product_id": product_id, "quantity": 2},
    )

    response = client.post(
        "/api/webhooks/remove-cart-item",
        json={"customer_id": customer_id, "product_name": "Webhook Product", "quantity": 1},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Reduced Webhook Product quantity by 1" in data["message"]

def test_checkout_cart_webhook(client: TestClient, db_session: Session):
    customer_response = client.post(
        "/api/customers",
        json={"name": "Webhook User", "phone": "4445556666", "email": "<EMAIL>"},
    )
    customer_id = customer_response.json()["id"]
    product_response = client.post(
        "/api/products",
        json={"name": "Webhook Product", "price": 25.0},
    )
    product_id = product_response.json()["id"]
    client.post(
        f"/api/customers/{customer_id}/cart/items",
        json={"product_id": product_id, "quantity": 2},
    )

    response = client.post(
        "/api/webhooks/checkout-cart",
        json={"customer_id": customer_id},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Your order has been processed" in data["message"]
