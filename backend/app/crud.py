import random
from typing import List, Optional

from sqlalchemy import and_, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, attributes

from . import models, schemas
from .performance_tracker import track_latency


# Customer CRUD operations
@track_latency("database", "get_customer")
def get_customer(db: Session, customer_id: int):
    return db.query(models.Customer).filter(models.Customer.id == customer_id).first()


@track_latency("database", "get_customer_by_phone")
def get_customer_by_phone(db: Session, phone: str):
    return db.query(models.Customer).filter(models.Customer.phone == phone).first()


@track_latency("database", "get_customers")
def get_customers(db: Session, skip: int = 0, limit: int = 100, search: Optional[str] = None):
    query = db.query(models.Customer).filter(models.Customer.is_active == True)

    if search:
        query = query.filter(
            or_(
                models.Customer.name.contains(search),
                models.Customer.email.contains(search),
                models.Customer.phone.contains(search),
            )
        )

    return query.offset(skip).limit(limit).all()


@track_latency("database", "create_customer")
def create_customer(db: Session, customer: schemas.CustomerCreate):
    """
    Creates a new customer and an associated active cart.
    The database handles the ID generation automatically.
    """
    db_customer = models.Customer(**customer.model_dump())
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)

    # Create an active cart for the new customer
    create_cart(db, schemas.CartCreate(customer_id=db_customer.id))

    return db_customer


@track_latency("database", "update_customer")
def update_customer(db: Session, customer_id: int, customer: schemas.CustomerUpdate):
    db_customer = get_customer(db, customer_id)
    if db_customer:
        update_data = customer.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_customer, field, value)
        db.commit()
        db.refresh(db_customer)
    return db_customer


@track_latency("database", "delete_customer")
def delete_customer(db: Session, customer_id: int):
    db_customer = get_customer(db, customer_id)
    if db_customer:
        db_customer.is_active = False
        db.commit()
    return db_customer


# Product CRUD operations
@track_latency("database", "get_product")
def get_product(db: Session, product_id: int):
    return db.query(models.Product).filter(models.Product.id == product_id).first()


@track_latency("database", "get_product_by_name")
def get_product_by_name(db: Session, name: str):
    return db.query(models.Product).filter(func.lower(models.Product.name) == func.lower(name)).first()


@track_latency("database", "get_product_by_sku")
def get_product_by_sku(db: Session, sku: str):
    return db.query(models.Product).filter(models.Product.sku == sku).first()


@track_latency("database", "get_products")
def get_products(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None,
):
    query = db.query(models.Product).filter(models.Product.is_active == True)

    if category:
        query = query.filter(models.Product.category == category)

    if search:
        query = query.filter(
            or_(
                models.Product.name.contains(search),
                models.Product.description.contains(search),
                models.Product.brand.contains(search),
            )
        )

    return query.offset(skip).limit(limit).all()


@track_latency("database", "create_product")
def create_product(db: Session, product: schemas.ProductCreate):
    db_product = models.Product(**product.model_dump())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product


@track_latency("database", "update_product")
def update_product(db: Session, product_id: int, product: schemas.ProductUpdate):
    db_product = get_product(db, product_id)
    if db_product:
        update_data = product.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_product, field, value)
        db.commit()
        db.refresh(db_product)
    return db_product


@track_latency("database", "update_product_enrichment")
def update_product_enrichment(db: Session, product_id: int, enrichment_data: dict):
    db_product = get_product(db, product_id)
    if db_product:
        db_product.enrichment = enrichment_data
        db.commit()
        db.refresh(db_product)
    return db_product


@track_latency("database", "delete_product")
def delete_product(db: Session, product_id: int):
    db_product = get_product(db, product_id)
    if db_product:
        db_product.is_active = False
        db.commit()
    return db_product


# Cart CRUD operations
@track_latency("database", "get_cart")
def get_cart(db: Session, cart_id: int):
    return db.query(models.Cart).filter(models.Cart.id == cart_id).first()


@track_latency("database", "get_customer_active_cart")
def get_customer_active_cart(db: Session, customer_id: int):
    return db.query(models.Cart).filter(models.Cart.customer_id == customer_id, models.Cart.status == "active").first()


@track_latency("database", "create_cart")
def create_cart(db: Session, cart: schemas.CartCreate):
    db_cart = models.Cart(**cart.model_dump())
    db.add(db_cart)
    db.commit()
    db.refresh(db_cart)
    return db_cart


@track_latency("database", "add_cart_item")
def add_cart_item(db: Session, cart_id: int, item: schemas.CartItemCreate):
    # Get product to get current price
    product = get_product(db, item.product_id)
    if not product:
        return None

    # Check if item already exists in cart
    existing_item = (
        db.query(models.CartItem)
        .filter(
            and_(
                models.CartItem.cart_id == cart_id,
                models.CartItem.product_id == item.product_id,
                models.CartItem.size == item.size,
                models.CartItem.color == item.color,
            )
        )
        .first()
    )

    if existing_item:
        # Update quantity
        existing_item.quantity += item.quantity
        db.commit()
        db.refresh(existing_item)
        return existing_item
    else:
        # Create new cart item
        db_item = models.CartItem(
            cart_id=cart_id,
            product_id=item.product_id,
            quantity=item.quantity,
            size=item.size,
            color=item.color,
            price=product.price,
        )
        db.add(db_item)
        db.commit()
        db.refresh(db_item)
        return db_item


@track_latency("database", "update_cart_item")
def update_cart_item(db: Session, item_id: int, item: schemas.CartItemUpdate):
    db_item = db.query(models.CartItem).filter(models.CartItem.id == item_id).first()
    if db_item:
        update_data = item.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_item, field, value)
        db.commit()
        db.refresh(db_item)
    return db_item


@track_latency("database", "remove_cart_item")
def remove_cart_item(db: Session, item_id: int, quantity: Optional[int] = None):
    db_item = db.query(models.CartItem).filter(models.CartItem.id == item_id).first()
    if db_item:
        if quantity is None or quantity >= db_item.quantity:
            db.delete(db_item)
        else:
            db_item.quantity -= quantity
        db.commit()
    return db_item


@track_latency("database", "calculate_cart_total")
def calculate_cart_total(db: Session, cart_id: int):
    cart_items = db.query(models.CartItem).filter(models.CartItem.cart_id == cart_id).all()
    total = sum(item.price * item.quantity for item in cart_items)

    # Update cart total
    cart = get_cart(db, cart_id)
    if cart:
        cart.total_amount = total
        db.commit()

    return total


@track_latency("database", "get_cart_summary")
def get_cart_summary(db: Session, customer_id: int) -> dict:
    cart = get_customer_active_cart(db, customer_id)
    if not cart:
        return {"error": "Cart not found."}

    calculate_cart_total(db, cart.id)
    db.refresh(cart)

    return schemas.Cart.from_orm(cart).model_dump(exclude_none=True)


# Call History CRUD operations
@track_latency("database", "create_call_history")
def create_call_history(db: Session, call: schemas.CallHistoryCreate):
    db_call = models.CallHistory(**call.model_dump(exclude_unset=True))
    db.add(db_call)
    db.commit()
    db.refresh(db_call)
    return db_call


@track_latency("database", "get_call_history")
def get_call_history(db: Session, call_id: int):
    return db.query(models.CallHistory).filter(models.CallHistory.id == call_id).first()


@track_latency("database", "get_customer_call_history")
def get_customer_call_history(db: Session, customer_id: int):
    return (
        db.query(models.CallHistory)
        .filter(models.CallHistory.customer_id == customer_id)
        .order_by(models.CallHistory.created_at.desc())
        .all()
    )


@track_latency("database", "update_call_history_details")
def update_call_history_details(db: Session, call_id: int, details: schemas.CallHistoryUpdate):
    db_call = get_call_history(db, call_id)
    if db_call:
        update_data = details.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(db_call, field, value)

        db.commit()
        db.refresh(db_call)
    return db_call


@track_latency("database", "add_conversation_log_event")
def add_conversation_log_event(db: Session, call_id: int, event: schemas.ConversationLogEvent):
    db_call = get_call_history(db, call_id)
    if db_call:
        if db_call.conversation_log is None:
            db_call.conversation_log = []
        db_call.conversation_log.append(event.model_dump(mode="json"))
        attributes.flag_modified(db_call, "conversation_log")
    return db_call


@track_latency("database", "update_call_history")
def update_call_history(db: Session, call_id: int, **kwargs):
    db_call = get_call_history(db, call_id)
    if db_call:
        for field, value in kwargs.items():
            if hasattr(db_call, field):
                setattr(db_call, field, value)
        db.commit()
        db.refresh(db_call)
    return db_call


# Order CRUD operations
@track_latency("database", "create_order")
def create_order(db: Session, order: schemas.OrderCreate):
    db_order = models.Order(**order.model_dump(exclude={"items"}))
    db.add(db_order)
    db.commit()
    db.refresh(db_order)
    for item in order.items:
        db_item = models.OrderItem(**item.model_dump(), order_id=db_order.id)
        db.add(db_item)
    db.commit()
    return db_order


@track_latency("database", "get_customer_order_history")
def get_customer_order_history(db: Session, customer_id: int):
    return (
        db.query(models.Order)
        .filter(models.Order.customer_id == customer_id)
        .order_by(models.Order.created_at.desc())
        .all()
    )


@track_latency("database", "get_call_context")
def get_call_context(db: Session, customer_id: int, webhook_data: Optional[dict] = None):
    """
    Gathers all necessary context for a call into a structured dictionary.
    """
    customer = get_customer(db, customer_id=customer_id)
    if not customer:
        return None

    cart = get_customer_active_cart(db, customer_id)
    cart_info = {"items": [], "total_amount": 0.0}
    if cart:
        calculate_cart_total(db, cart.id)
        db.refresh(cart)
        cart_info["total_amount"] = cart.total_amount
        for item in cart.items:
            cart_info["items"].append(
                {
                    "product_name": item.product.name,
                    "quantity": item.quantity,
                    "size": item.size,
                    "color": item.color,
                    "price": item.price,
                }
            )

    return {
        "customer_info": {
            "customer_id": customer.id,
            "name": customer.name,
            "email": customer.email,
            "notes": customer.notes,
            "enrichment": customer.enrichment,
        },
        "cart_info": cart_info,
        "call_context": webhook_data or {},
    }


@track_latency("database", "generate_system_prompt")
def generate_system_prompt(
    db: Session,
    customer_id: int,
    custom_prompt: Optional[str] = None,
    custom_first_message: Optional[str] = None,
    webhook_data: Optional[dict] = None,
):
    """
    Generates a system prompt and first message for a call.
    """
    customer = get_customer(db, customer_id=customer_id)
    if not customer:
        return None

    cart = get_customer_active_cart(db, customer_id)

    cart_details = "The customer's cart is currently empty."
    if cart and cart.items:
        # Calculate cart total before generating details
        calculate_cart_total(db, cart.id)
        db.refresh(cart)

        items_str = []
        for item in cart.items:
            items_str.append(
                f"- {item.quantity} x {item.product.name} ({item.size}, {item.color}) at ${item.price} each."
            )

        cart_details = f"""
Here are the items in the customer's cart:
{"\n".join(items_str)}
Total amount: ${cart.total_amount}
"""

    # Base prompt
    system_prompt = (
        custom_prompt
        or "You are a friendly and helpful AI shopping assistant. Your responses will be converted to speech, so please speak naturally and conversationally. Do not use any formatting like Markdown, lists, or asterisks. For example, instead of saying 'Here are the items: * Item 1, * Item 2', say 'The items are Item 1 and Item 2.'"
    )

    # Add customer and cart context
    full_context = f"""{system_prompt}

Here is some information about the customer you are speaking with:
- Name: {customer.name}
- Email: {customer.email}
- Notes: {customer.notes}
- Call Enrichment Data: {customer.enrichment}

{cart_details}

Additional context for this call:
{webhook_data or {}}
""".strip()

    # First message generation
    first_message = custom_first_message or f"Hello {customer.name}! How can I help you today?"
    first_message = first_message.replace("{customer_name}", customer.name)

    return {"system_prompt": full_context, "first_message": first_message}


# Performance Metric CRUD operations
def create_performance_metric(db: Session, metric: schemas.PerformanceMetricCreate):
    db_metric = models.PerformanceMetric(**metric.model_dump())
    db.add(db_metric)
    db.commit()
    db.refresh(db_metric)
    return db_metric


def get_performance_metrics(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.PerformanceMetric).offset(skip).limit(limit).all()


# Prompt Test CRUD operations
@track_latency("database", "get_prompt_tests")
def get_prompt_tests(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.PromptTest).offset(skip).limit(limit).all()


@track_latency("database", "get_prompt_test")
def get_prompt_test(db: Session, prompt_test_id: int):
    return db.query(models.PromptTest).filter(models.PromptTest.id == prompt_test_id).first()


@track_latency("database", "create_prompt_test")
def create_prompt_test(db: Session, prompt_test: schemas.PromptTestCreate):
    db_prompt_test = models.PromptTest(**prompt_test.model_dump())
    db.add(db_prompt_test)
    db.commit()
    db.refresh(db_prompt_test)
    return db_prompt_test


@track_latency("database", "update_prompt_test")
def update_prompt_test(db: Session, prompt_test_id: int, prompt_test: schemas.PromptTestUpdate):
    db_prompt_test = get_prompt_test(db, prompt_test_id)
    if db_prompt_test:
        update_data = prompt_test.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_prompt_test, field, value)
        db.commit()
        db.refresh(db_prompt_test)
    return db_prompt_test


@track_latency("database", "delete_prompt_test")
def delete_prompt_test(db: Session, prompt_test_id: int):
    db_prompt_test = get_prompt_test(db, prompt_test_id)
    if db_prompt_test:
        db.delete(db_prompt_test)
        db.commit()
    return db_prompt_test
