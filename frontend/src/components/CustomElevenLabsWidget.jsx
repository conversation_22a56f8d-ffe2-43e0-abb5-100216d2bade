import React, { useState, useRef } from 'react';
import { Mic, PhoneOff } from 'lucide-react';

const ELEVENLABS_API_KEY = import.meta.env.VITE_ELEVENLABS_API_KEY;
const ELEVENLABS_VOICE_ID = import.meta.env.VITE_ELEVENLABS_VOICE_ID;

export default function CustomElevenLabsWidget({ customerName }) {
  const [isCallActive, setIsCallActive] = useState(false);
  const [transcript, setTranscript] = useState([]);
  const socketRef = useRef(null);
  const mediaRecorderRef = useRef(null);

  const handleToggleCall = async () => {
    if (isCallActive) {
      // Stop the call
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
      if (socketRef.current) {
        // Send end of stream message
        socketRef.current.send(JSON.stringify({ text: "" }));
        socketRef.current.close();
      }
      setIsCallActive(false);
      setTranscript(prev => [...prev, { speaker: 'System', text: 'Call ended.' }]);
    } else {
      // Start the call
      setIsCallActive(true);
      setTranscript([{ speaker: 'System', text: 'Connecting to ElevenLabs...' }]);

      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const mediaRecorder = new MediaRecorder(stream);
        mediaRecorderRef.current = mediaRecorder;

        const ws = new WebSocket(`wss://api.elevenlabs.io/v1/speech-to-speech/streaming/from-microphone?voice_id=${ELEVENLABS_VOICE_ID}`);
        socketRef.current = ws;

        ws.onopen = () => {
          console.log('WebSocket connected');
          // Send the initial configuration message
          const bosMessage = {
            text: " ",
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.8
            },
            generation_config: {
              chunk_length_schedule: [50]
            },
            api_key: ELEVENLABS_API_KEY,
          };
          ws.send(JSON.stringify(bosMessage));
          setTranscript(prev => [...prev, { speaker: 'System', text: 'Connected. Start speaking.' }]);
          mediaRecorder.start(500); // Start recording and send data every 500ms
        };

        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          if (data.text) {
            setTranscript(prev => [...prev, { speaker: data.speaker || 'Assistant', text: data.text }]);
          }
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          setTranscript(prev => [...prev, { speaker: 'System', text: 'An error occurred.' }]);
          setIsCallActive(false);
        };

        ws.onclose = () => {
          console.log('WebSocket disconnected');
          setIsCallActive(false);
        };

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0 && ws.readyState === WebSocket.OPEN) {
            const audioData = {
              audio: event.data
            }
            ws.send(JSON.stringify({ "audio": audioData }));
          }
        };

      } catch (error) {
        console.error('Error starting call:', error);
        setTranscript([{ speaker: 'System', text: 'Could not start the call. Please check your microphone permissions.' }]);
        setIsCallActive(false);
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-xl w-full h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Voice Assistant</h3>
          <p className="text-sm text-gray-500">
            {customerName && `Talking with ${customerName}`}
          </p>
        </div>
        <button
          onClick={handleToggleCall}
          className={`btn ${isCallActive ? 'btn-error' : 'btn-success'} btn-md`}
        >
          {isCallActive ? (
            <><PhoneOff className="h-4 w-4 mr-2" /> Stop Call</>
          ) : (
            <><Mic className="h-4 w-4 mr-2" /> Start Call</>
          )}
        </button>
      </div>
      <div className="flex-1 p-4 overflow-y-auto">
        {transcript.map((entry, index) => (
          <div key={index} className="mb-2">
            <span className={`font-bold ${entry.speaker === 'User' ? 'text-blue-500' : 'text-green-500'}`}>{entry.speaker}:</span> {entry.text}
          </div>
        ))}
      </div>
    </div>
  );
}
