from fastapi import APIRouter, Depends
from typing import List
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

@router.get("/metrics", response_model=List[schemas.PerformanceMetric])
def read_metrics(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """
    Retrieve all collected performance metrics from the database.
    """
    metrics = crud.get_performance_metrics(db, skip=skip, limit=limit)
    return metrics