import requests
import json
import asyncio
import websockets
import os
from dotenv import load_dotenv

# Construct an absolute path to the .env file
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

BASE_URL = os.getenv("BASE_URL")
if not BASE_URL:
    raise ValueError(f"BASE_URL not found in {dotenv_path}")


async def make_web_call(call_history_id: int):
    """
    Initiates a web call using WebSockets.
    """
    print("\n--- Initiating Web Call ---")
    ws_url = f"wss://{BASE_URL}/api/voice/web-call"
    try:
        async with websockets.connect(ws_url) as websocket:
            # Send initialization message
            init_message = {
                "callHistoryId": call_history_id,
            }
            await websocket.send(json.dumps(init_message))
            print(f"Sent init message: {init_message}")

            # Receive the first message from the server
            first_message = await websocket.recv()
            print(f"Received from server: {first_message}")

            # In a real scenario, you would handle audio streaming here.
            # For this script, we'll just close the connection.
            await websocket.close()
            print("Web call finished.")

    except websockets.exceptions.ConnectionClosed as e:
        print(f"Web call connection closed: {e}")
    except Exception as e:
        print(f"Error during web call: {e}")


if __name__ == "__main__":
    # Replace with a valid customer ID and phone number
    CUSTOMER_ID = 1

    # To test the web call, you need a call_history_id.
    # You can get this from the response of a web call initiation endpoint
    # or by creating a call history record directly in the database.
    # For now, we'll use a placeholder.
    CALL_HISTORY_ID = 1

    # Running the async web call function
    asyncio.run(make_web_call(CALL_HISTORY_ID))
