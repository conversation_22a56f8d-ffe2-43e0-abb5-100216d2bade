from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from .. import crud, schemas
from ..database import get_db

router = APIRouter(prefix="/prompt-tests", tags=["prompt-tests"])


@router.get("", response_model=List[schemas.PromptTest])
def read_prompt_tests(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
):
    """
    Retrieve all prompt tests
    """
    prompt_tests = crud.get_prompt_tests(db, skip=skip, limit=limit)
    return prompt_tests


@router.post("", response_model=schemas.PromptTest)
def create_prompt_test(prompt_test: schemas.PromptTestCreate, db: Session = Depends(get_db)):
    """
    Create a new prompt test
    """
    return crud.create_prompt_test(db=db, prompt_test=prompt_test)


@router.get("/{prompt_test_id}", response_model=schemas.PromptTest)
def read_prompt_test(prompt_test_id: int, db: Session = Depends(get_db)):
    """
    Get a specific prompt test by ID
    """
    db_prompt_test = crud.get_prompt_test(db, prompt_test_id=prompt_test_id)
    if db_prompt_test is None:
        raise HTTPException(status_code=404, detail="Prompt test not found")
    return db_prompt_test


@router.put("/{prompt_test_id}", response_model=schemas.PromptTest)
def update_prompt_test(
    prompt_test_id: int,
    prompt_test: schemas.PromptTestUpdate,
    db: Session = Depends(get_db),
):
    """
    Update a prompt test
    """
    db_prompt_test = crud.update_prompt_test(db, prompt_test_id=prompt_test_id, prompt_test=prompt_test)
    if db_prompt_test is None:
        raise HTTPException(status_code=404, detail="Prompt test not found")
    return db_prompt_test


@router.delete("/{prompt_test_id}")
def delete_prompt_test(prompt_test_id: int, db: Session = Depends(get_db)):
    """
    Delete a prompt test
    """
    db_prompt_test = crud.delete_prompt_test(db, prompt_test_id=prompt_test_id)
    if db_prompt_test is None:
        raise HTTPException(status_code=404, detail="Prompt test not found")
    return {"message": "Prompt test deleted successfully"}
