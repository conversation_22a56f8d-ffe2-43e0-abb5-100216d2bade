import logging

from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse

from ..config import settings
from ..performance_tracker import track_latency

logger = logging.getLogger(__name__)


class TwilioService:
    def __init__(self):
        self.client = Client(settings.twilio_account_sid, settings.twilio_auth_token)
        self.from_number = settings.twilio_phone_number

    @track_latency("twilio", "make_outbound_call")
    def make_outbound_call(self, to_number: str, call_history_id: int):
        """
        Initiate an outbound call using Twilio and connect it to our TwiML endpoint for ConversationRelay.
        """
        try:
            # Construct the TwiML URL for our new endpoint
            # The base_url from settings should be the public ngrok URL, without protocol
            # e.g., "skilled-lemur-yearly.ngrok-free.app"
            twiml_url = (
                f"https://{settings.base_url}/api/twilio/twiml/{call_history_id}"
            )

            call = self.client.calls.create(
                to=to_number, from_=self.from_number, url=twiml_url, method="POST"
            )

            logger.info(
                f"Outbound call initiated with ConversationRelay: {call.sid} to {to_number}"
            )
            return {
                "success": True,
                "call_sid": call.sid,
                "status": call.status,
                "to": to_number,
                "from": self.from_number,
            }

        except Exception as e:
            logger.error(f"Failed to initiate call to {to_number}: {str(e)}")
            return {"success": False, "error": str(e)}

    @track_latency("twilio", "get_call_status")
    def get_call_status(self, call_sid: str):
        """
        Get the current status of a call
        """
        try:
            call = self.client.calls(call_sid).fetch()
            return {
                "success": True,
                "call_sid": call_sid,
                "status": call.status,
                "duration": call.duration,
                "start_time": call.start_time,
                "end_time": call.end_time,
            }
        except Exception as e:
            logger.error(f"Failed to get call status for {call_sid}: {str(e)}")
            return {"success": False, "error": str(e)}

    @track_latency("twilio", "end_call")
    def end_call(self, call_sid: str):
        """
        End an active call
        """
        try:
            call = self.client.calls(call_sid).update(status="completed")
            logger.info(f"Call ended: {call_sid}")
            return {"success": True, "call_sid": call_sid, "status": call.status}
        except Exception as e:
            logger.error(f"Failed to end call {call_sid}: {str(e)}")
            return {"success": False, "error": str(e)}

    def generate_twiml_response(self, message: str, next_action: str = None):
        """
        Generate TwiML response for voice interactions
        """
        response = VoiceResponse()

        if message:
            response.say(message, voice="alice")

        if next_action == "gather":
            gather = response.gather(input="speech", timeout=5, speech_timeout="auto")
            gather.say("Please speak your response.")
        elif next_action == "hangup":
            response.hangup()
        elif next_action == "redirect":
            response.redirect("/voice/continue")

        return str(response)

    @track_latency("twilio", "create_conference")
    def create_conference(self, conference_name: str, participant_number: str):
        """
        Create a conference call
        """
        try:
            # Create conference
            conference = self.client.conferences(conference_name).fetch()

            # Add participant
            participant = self.client.conferences(conference_name).participants.create(
                from_=self.from_number, to=participant_number
            )

            return {
                "success": True,
                "conference_name": conference_name,
                "participant_sid": participant.call_sid,
            }

        except Exception as e:
            logger.error(f"Failed to create conference {conference_name}: {str(e)}")
            return {"success": False, "error": str(e)}



# Global instance
twilio_service = TwilioService()
