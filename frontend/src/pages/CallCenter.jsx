import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useQuery } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import { PhoneCall, PhoneOff, Mic, Users, Clock, Eye, MessageSquare, TestTube } from 'lucide-react';
import { customerAPI, voiceAPI } from '../utils/api';
import { formatPhoneNumber, formatDuration } from '../utils/helpers';
import LoadingSpinner from '../components/LoadingSpinner';
import CallTemplates from '../components/CallTemplates';
import LiveTranscript from '../components/LiveTranscript';
import CustomerSelector from '../components/CustomerSelector';
import WebCallWidget from '../components/WebCallWidget';
import PromptTestTab from '../components/PromptTestTab';
import toast from 'react-hot-toast';
import ContextPreviewModal from '../components/ContextPreviewModal';
import OrderHistoryModal from '../components/OrderHistoryModal';
import ConversationLog from '../components/ConversationLog';
import Insights from '../components/Insights';
import { callTemplates } from '../components/CallTemplates';

export default function CallCenter() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('call-center');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customPrompt, setCustomPrompt] = useState(callTemplates[0].prompt);
  const [customFirstMessage, setCustomFirstMessage] = useState(callTemplates[0].firstMessage);
  const [webhookData, setWebhookData] = useState(JSON.stringify(callTemplates[0].webhookData, null, 2));
  const [activeCall, setActiveCall] = useState(null);
  const [callStatus, setCallStatus] = useState('idle'); // idle, dialing, connected, ended
  const [webCallStatus, setWebCallStatus] = useState('idle'); // idle, connecting, connected, ended
  const [textChatStatus, setTextChatStatus] = useState('idle'); // idle, connecting, connected, ended
  const [callDuration, setCallDuration] = useState(0);
  const [conversationLog, setConversationLog] = useState([]);
  const [insights, setInsights] = useState([]);
  const [actions, setActions] = useState([]);
  const [isWebCallWidgetOpen, setIsWebCallWidgetOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isOrderHistoryModalOpen, setIsOrderHistoryModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [promptPreview, setPromptPreview] = useState({ system_prompt: '', first_message: '' });
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  const ws = useRef(null);
  const callDurationRef = useRef(callDuration);
  const activeCallRef = useRef(activeCall);
  const conversationLogRef = useRef(conversationLog);
  const insightsRef = useRef(null);
  const callUpdatesWs = useRef(null);

  const scrollToInsights = () => {
    insightsRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    callDurationRef.current = callDuration;
  }, [callDuration]);

  useEffect(() => {
    activeCallRef.current = activeCall;
  }, [activeCall]);

  useEffect(() => {
    conversationLogRef.current = conversationLog;
  }, [conversationLog]);

  const { data: customers, isLoading: isLoadingCustomers } = useQuery(
    'customers',
    () => customerAPI.getAll(),
    {
      select: (response) => (Array.isArray(response.data) ? response.data : []),
    }
  );

  useEffect(() => {
    const customerId = searchParams.get('customer_id');
    if (customers && customers.length > 0 && !selectedCustomer) {
      const customerFromUrl = customerId
        ? customers.find(c => c.id.toString() === customerId)
        : null;

      setSelectedCustomer(customerFromUrl || customers[0]);
    }
  }, [customers, searchParams, selectedCustomer]);

  useEffect(() => {
    if (activeCall?.call_history_id) {
      const wsUrl = `${voiceAPI.getCallUpdatesSocketUrl()}/${activeCall.call_history_id}`;
      callUpdatesWs.current = new WebSocket(wsUrl);

      callUpdatesWs.current.onopen = () => {
        console.log('Call updates WebSocket connected');
      };

      callUpdatesWs.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      callUpdatesWs.current.onclose = () => {
        console.log('Call updates WebSocket disconnected');
      };

      return () => {
        callUpdatesWs.current.close();
      };
    }
  }, [activeCall]);

  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    if (customer) {
      setSearchParams({ customer_id: customer.id });
    } else {
      setSearchParams({});
    }
  };

  useEffect(() => {
    let interval;
    if (callStatus === 'connected' || webCallStatus === 'connected' || textChatStatus === 'connected') {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    } else {
      setCallDuration(0);
    }
    return () => clearInterval(interval);
  }, [callStatus, webCallStatus, textChatStatus]);

  const handlePreview = async () => {
    if (!selectedCustomer) {
      toast.error('Please select a customer to preview the prompt.');
      return;
    }
    if (!customPrompt || !customFirstMessage) {
      toast.error('Please set a custom prompt and first message before previewing.');
      return;
    }
    setIsPreviewLoading(true);
    setIsPreviewModalOpen(true);
    try {
      let parsedWebhookData = {};
      try {
        parsedWebhookData = JSON.parse(webhookData);
      } catch (error) {
        toast.error('Additional Context is not valid JSON.');
        setIsPreviewLoading(false);
        return;
      }

      const requestBody = {
        custom_prompt: customPrompt || null,
        custom_first_message: customFirstMessage || null,
        webhook_data: parsedWebhookData,
      };

      const response = await customerAPI.getPromptPreview(selectedCustomer.id, requestBody);
      setPromptPreview(response.data);

    } catch (error) {
      toast.error('Failed to load prompt preview.');
      setPromptPreview({ system_prompt: 'Error loading preview.', first_message: 'Error loading preview.' });
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const handleInitiateCall = async () => {
    if (!selectedCustomer) {
      toast.error('Please select a customer');
      return;
    }
    if (!customPrompt || !customFirstMessage) {
      toast.error('Please set a custom prompt and first message before starting a call.');
      return;
    }

    try {
      setCallStatus('dialing');
      let parsedWebhookData = {};
      try {
        parsedWebhookData = JSON.parse(webhookData);
      } catch (error) {
        console.warn('Invalid webhook data JSON, using empty object');
      }

      const callData = {
        customer_id: selectedCustomer.id,
        phone_number: selectedCustomer.phone,
        custom_prompt: customPrompt || null,
        custom_first_message: customFirstMessage || null,
        webhook_data: parsedWebhookData,
      };

      const response = await voiceAPI.initiateCall(callData);

      if (response.data.success) {
        setActiveCall({
          type: 'phone',
          call_sid: response.data.call_sid,
          call_history_id: response.data.call_history_id,
          customer_name: selectedCustomer.name,
          phone_number: selectedCustomer.phone,
        });
        setCallStatus('connected');
        toast.success('Call initiated successfully');
      } else {
        throw new Error(response.data.message || 'Failed to initiate call');
      }
    } catch (error) {
      setCallStatus('idle');
      toast.error(error.response?.data?.detail || 'Failed to initiate call');
    }
  };

  const handleEndCall = async () => {
    if (activeCall?.type === 'phone' && activeCall?.call_sid) {
      try {
        await voiceAPI.endCall(activeCall.call_sid);
        toast.success('Call ended');
      } catch (error) {
        toast.error('Failed to end call');
      } finally {
        setCallStatus('ended');
        setTimeout(() => {
          setCallStatus('idle');
          setActiveCall(null);
          setConversationLog([]);
        }, 2000);
      }
    } else if (activeCall?.type === 'web') {
      setIsWebCallWidgetOpen(false);
    } else if (activeCall?.type === 'text') {
      if (ws.current) {
        ws.current.close();
      }
    }
  };

  const handleWebCallStart = async () => {
    if (!selectedCustomer) {
      toast.error('Please select a customer');
      return;
    }
    if (!customPrompt || !customFirstMessage) {
      toast.error('Please set a custom prompt and first message before starting a call.');
      return;
    }
    setConversationLog([]);
    setInsights([]);
    setActions([]);
    setWebCallStatus('connecting');

    try {
      const response = await voiceAPI.initiateWebCall({
        customer_id: selectedCustomer.id,
        custom_prompt: customPrompt,
        custom_first_message: customFirstMessage.replace('{customer_name}', selectedCustomer.name),
      });
      if (response.data.success) {
        setActiveCall({
          type: 'web',
          call_history_id: response.data.call_history_id,
          customer_name: selectedCustomer.name,
          phone_number: 'Web Call',
        });
        setIsWebCallWidgetOpen(true);
      } else {
        throw new Error(response.data.message || 'Failed to initiate web call');
      }
    } catch (error) {
      setWebCallStatus('idle');
      toast.error(error.response?.data?.detail || 'Failed to initiate web call');
    }
  };

  const handleWebCallConnect = () => {
    setWebCallStatus('connected');
    toast.success('Web call connected');
  };

  const handleWebCallEnd = useCallback(async () => {
    setWebCallStatus('ended');
    toast('Web call ended');

    const currentActiveCall = activeCallRef.current;
    const currentConversationLog = conversationLogRef.current;
    const currentCallDuration = callDurationRef.current;

    if (currentActiveCall?.call_history_id !== null && currentActiveCall?.call_history_id !== undefined) {
      try {
        const payload = {
          conversation_log: currentConversationLog,
          call_duration: currentCallDuration,
          status: 'completed',
        };
        await voiceAPI.updateCallHistory(currentActiveCall.call_history_id, payload);
        toast.success('Conversation log saved.');
      } catch (error) {
        console.error('Failed to save conversation log:', error);
        toast.error('Failed to save conversation log.');
      }
    }

    setWebCallStatus('idle');
    setActiveCall(null);
  }, []);

  const handleTextChatStart = async () => {
    if (!selectedCustomer) {
      toast.error('Please select a customer');
      return;
    }
    if (!customPrompt || !customFirstMessage) {
      toast.error('Please set a custom prompt and first message before starting a chat.');
      return;
    }
    setConversationLog([]);
    setInsights([]);
    setActions([]);
    setTextChatStatus('connecting');

    try {
      const response = await voiceAPI.initiateTextChat({
        customer_id: selectedCustomer.id,
        custom_prompt: customPrompt,
        custom_first_message: customFirstMessage.replace('{customer_name}', selectedCustomer.name),
      });
      if (response.data.success) {
        setActiveCall({
          type: 'text',
          call_history_id: response.data.call_history_id,
          customer_name: selectedCustomer.name,
          phone_number: 'Text Chat',
        });
        setupTextChatWebSocket(response.data.call_history_id);
      } else {
        throw new Error(response.data.message || 'Failed to initiate text chat');
      }
    } catch (error) {
      setTextChatStatus('idle');
      toast.error(error.response?.data?.detail || 'Failed to initiate text chat');
    }
  };

  const setupTextChatWebSocket = (callHistoryId) => {
    const wsUrl = voiceAPI.getTextChatSocketUrl();
    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      console.log('Text chat WebSocket connected');
      setTextChatStatus('connected');
      toast.success('Text chat connected');
      ws.current.send(JSON.stringify({
        type: 'init',
        data: {
          call_history_id: callHistoryId,
          customer_id: selectedCustomer.id,
          custom_prompt: customPrompt,
          custom_first_message: customFirstMessage.replace('{customer_name}', selectedCustomer.name),
          webhook_data: JSON.parse(webhookData),
        }
      }));
    };

    ws.current.onmessage = (event) => {
      console.log('Received WebSocket message:', event.data);
      const message = JSON.parse(event.data);
      handleWebSocketMessage(message);
    };

    ws.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      toast.error('Text chat connection error.');
      handleTextChatEnd();
    };

    ws.current.onclose = (event) => {
      console.log('Text chat WebSocket disconnected:', event);
      handleTextChatEnd();
    };
  };

  const handleTextChatEnd = useCallback(async () => {
    setTextChatStatus('ended');

    const currentActiveCall = activeCallRef.current;
    if (currentActiveCall?.type === 'text') {
      toast('Text chat ended');
      const currentCallDuration = callDurationRef.current;

      if (currentActiveCall?.call_history_id) {
        try {
          const payload = {
            call_duration: currentCallDuration,
            status: 'completed',
          };
          await voiceAPI.updateCallHistory(currentActiveCall.call_history_id, payload);
          toast.success('Chat history saved.');
        } catch (error) {
          console.error('Failed to save chat history:', error);
          toast.error('Failed to save chat history.');
        }
      }
    }

    setTextChatStatus('idle');
    setActiveCall(null);
  }, []);

  const handleWebSocketMessage = (message) => {
    if (message.type === 'transcript' || message.type === 'human') {
      const newLogEntry = {
        event_type: 'transcript',
        source: message.sender,
        data: { content: message.transcript },
        timestamp: new Date().toISOString(),
      };
      setConversationLog(prev => [...prev, newLogEntry]);
    } else if (message.type === 'insight') {
      setInsights(prev => [...prev, message.insight]);
    } else if (message.type === 'action') {
      setActions(prev => [...prev, message.action]);
    } else if (message.type === 'conversation_log') {
      setConversationLog(prev => [...prev, message.log]);
    }
  };

  const handleSendTextMessage = (text) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      const message = { type: 'user_message', text };
      ws.current.send(JSON.stringify(message));
      const newLogEntry = {
        event_type: 'transcript',
        source: 'user',
        data: { content: text },
        timestamp: new Date().toISOString(),
      };
      setConversationLog(prev => [...prev, newLogEntry]);
    }
  };

  const handleTemplateSelect = (template) => {
    setCustomPrompt(template.prompt);
    setCustomFirstMessage(template.firstMessage);
    if (template.webhookData) {
      setWebhookData(JSON.stringify(template.webhookData, null, 2));
    }
  };

  const handleOrderClick = (order) => {
    setSelectedOrder(order);
    setIsOrderHistoryModalOpen(true);
  };

  const getStatusColor = () => {
    const status = activeCall?.type === 'web' ? webCallStatus : activeCall?.type === 'text' ? textChatStatus : callStatus;
    switch (status) {
      case 'dialing':
      case 'connecting':
        return 'text-yellow-600';
      case 'connected': return 'text-green-600';
      case 'ended': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const isCallActive = callStatus !== 'idle' || webCallStatus !== 'idle' || textChatStatus !== 'idle';

  if (isLoadingCustomers) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <ContextPreviewModal
        isOpen={isPreviewModalOpen}
        onClose={() => setIsPreviewModalOpen(false)}
        systemPrompt={isPreviewLoading ? 'Loading...' : promptPreview.system_prompt}
        firstMessage={isPreviewLoading ? 'Loading...' : promptPreview.first_message}
      />

      <OrderHistoryModal
        isOpen={isOrderHistoryModalOpen}
        onClose={() => setIsOrderHistoryModalOpen(false)}
        order={selectedOrder}
      />

      {isWebCallWidgetOpen && selectedCustomer && activeCall && (
        <WebCallWidget
          customerId={selectedCustomer.id}
          callHistoryId={activeCall.call_history_id}
          onClose={() => {
            setIsWebCallWidgetOpen(false);
            handleWebCallEnd();
          }}
          onConnect={handleWebCallConnect}
          onTranscriptUpdate={handleWebSocketMessage}
          customPrompt={customPrompt}
          customFirstMessage={customFirstMessage.replace('{customer_name}', selectedCustomer.name)}
          webhookData={webhookData}
          conversationLog={conversationLog}
        />
      )}

      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Call Center</h1>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'call-center'
              ? 'Select a customer to make outbound calls with AI voice agents.'
              : 'Test your prompts with phone calls, web calls, and text chat without customer selection.'
            }
          </p>
        </div>
        {activeTab === 'call-center' && (
          <div className="flex items-center space-x-2">
            {!isCallActive ? (
              <>
                <button
                  onClick={() => {
                    handleInitiateCall();
                    scrollToInsights();
                  }}
                  disabled={!selectedCustomer}
                  className="btn btn-success btn-md"
                >
                  <PhoneCall className="h-4 w-4 mr-2" />
                  Start Phone Call
                </button>
                <button
                  onClick={() => {
                    handleWebCallStart();
                    scrollToInsights();
                  }}
                  disabled={!selectedCustomer}
                  className="btn btn-primary btn-md"
                >
                  <Mic className="h-4 w-4 mr-2" />
                  Start Web Call
                </button>
                <button
                  onClick={() => {
                    handleTextChatStart();
                    scrollToInsights();
                  }}
                  disabled={!selectedCustomer}
                  className="btn btn-info btn-md"
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Start Text Chat
                </button>
                <button
                  onClick={handlePreview}
                  disabled={!selectedCustomer || isPreviewLoading}
                  className="btn btn-secondary btn-md"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {isPreviewLoading ? 'Loading...' : 'Preview'}
                </button>
              </>
            ) : (
              <button
                onClick={handleEndCall}
                className="btn btn-danger btn-md"
              >
                <PhoneOff className="h-4 w-4 mr-2" />
                End Call
              </button>
            )}
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('call-center')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'call-center'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
          >
            <Users className="h-4 w-4 mr-2 inline" />
            Call Center
          </button>
          <button
            onClick={() => setActiveTab('prompt-test')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'prompt-test'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
          >
            <TestTube className="h-4 w-4 mr-2 inline" />
            Prompt Test
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'call-center' ? (
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          <div className="lg:col-span-8 space-y-6">
            <CustomerSelector
              customers={customers || []}
              selectedCustomer={selectedCustomer}
              onCustomerSelect={handleCustomerSelect}
              onOrderClick={handleOrderClick}
            />

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Call Configuration</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Custom System Prompt
                  </label>
                  <textarea
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    rows={6}
                    className="textarea"
                    placeholder="You are a friendly customer service representative..."
                    disabled={isCallActive}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Message
                  </label>
                  <textarea
                    value={customFirstMessage}
                    onChange={(e) => setCustomFirstMessage(e.target.value)}
                    rows={3}
                    className="textarea"
                    placeholder="Hi {customer_name}! This is Sarah from..."
                    disabled={isCallActive}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Context (JSON)
                  </label>
                  <textarea
                    value={webhookData}
                    onChange={(e) => setWebhookData(e.target.value)}
                    rows={5}
                    className="textarea font-mono text-sm"
                    placeholder='{"priority": "high", "campaign": "cart_abandonment"}'
                    disabled={isCallActive}
                  />
                </div>
              </div>
            </div>

            <CallTemplates onTemplateSelect={handleTemplateSelect} />
          </div>

          <div className="lg:col-span-4 space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Call Status</h3>
              {activeCall ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Customer:</span>
                    <span className="text-sm font-medium">{activeCall.customer_name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Contact:</span>
                    <span className="text-sm font-medium">
                      {activeCall.type === 'phone' ? formatPhoneNumber(activeCall.phone_number) :
                        activeCall.type === 'web' ? 'Web Browser' : 'Text Chat'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Status:</span>
                    <span className={`text-sm font-medium ${getStatusColor()}`}>
                      {activeCall.type === 'web'
                        ? webCallStatus.charAt(0).toUpperCase() + webCallStatus.slice(1)
                        : activeCall.type === 'text'
                          ? textChatStatus.charAt(0).toUpperCase() + textChatStatus.slice(1)
                          : callStatus.charAt(0).toUpperCase() + callStatus.slice(1)}
                    </span>
                  </div>
                  {(callStatus === 'connected' || webCallStatus === 'connected' || textChatStatus === 'connected') && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Duration:</span>
                      <span className="text-sm font-medium">{formatDuration(callDuration)}</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">Select a customer to call</p>
                </div>
              )}
            </div>

            <div ref={insightsRef}>
              <Insights
                insights={insights}
                actions={actions}
                isActive={webCallStatus === 'connected' || textChatStatus === 'connected'}
              />
            </div>

            <LiveTranscript
              conversationLog={conversationLog}
              isActive={callStatus === 'connected' || webCallStatus === 'connected' || textChatStatus === 'connected'}
              isTextChatActive={textChatStatus === 'connected'}
              onSendMessage={handleSendTextMessage}
            />

            {activeCall && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <ConversationLog log={conversationLog} />
              </div>
            )}
          </div>
        </div>
      ) : (
        <PromptTestTab />
      )}
    </div>
  );
}
