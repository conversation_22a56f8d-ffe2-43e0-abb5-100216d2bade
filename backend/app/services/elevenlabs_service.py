import asyncio
import base64
import logging

from elevenlabs.client import ElevenLabs
from fastapi import WebSocket, WebSocketDisconnect

from ..config import settings
from ..performance_tracker import track_latency

logger = logging.getLogger(__name__)


class ElevenLabsService:
    def __init__(self):
        self.client = ElevenLabs(api_key=settings.elevenlabs_api_key)
        if settings.elevenlabs_api_key:
            logger.info("ElevenLabsService initialized with API key.")
        else:
            logger.warning("ElevenLabsService is missing API key in settings.")

    async def transcribe_stream(self, websocket: WebSocket, audio_queue: asyncio.Queue):
        try:
            while True:
                data = await websocket.receive_bytes()
                # This is a placeholder for actual transcription.
                # ElevenLabs Python SDK v1 doesn't directly support streaming transcription yet.
                # We would need to use a different library or a raw API endpoint for this.
                # For now, we'll just pass the audio through to be processed.
                await audio_queue.put(data)
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected in transcribe_stream.")
            await audio_queue.put(None)  # Signal the end of the stream
        except Exception as e:
            logger.error(f"Error in transcribe_stream: {e}")
            await audio_queue.put(None)

    @track_latency("tts", "elevenlabs_text_to_speech_stream")
    async def text_to_speech_stream(self, text_generator, websocket: WebSocket):
        voice_id = settings.elevenlabs_voice_id
        model_id = "eleven_turbo_v2_5"  # Changed to v2_5 to support language_code
        loop = asyncio.get_running_loop()

        def text_iterator():
            while True:
                try:
                    chunk_future = asyncio.run_coroutine_threadsafe(
                        text_generator.__anext__(), loop
                    )
                    chunk = chunk_future.result()
                    yield chunk
                except StopAsyncIteration:
                    break
                except Exception as e:
                    logger.error(f"Error in text_iterator: {e}")
                    break

        def process_audio_stream():
            try:
                stream_kwargs = {
                    "text": text_iterator(),
                    "voice_id": voice_id,
                    "model_id": model_id,
                    "output_format": f"pcm_24000",
                }
                if settings.elevenlabs_language_code:
                    stream_kwargs["language_code"] = settings.elevenlabs_language_code

                audio_stream = self.client.text_to_speech.stream(**stream_kwargs)

                for audio_chunk in audio_stream:
                    if websocket.client_state == "DISCONNECTED":
                        break
                    audio_b64 = base64.b64encode(audio_chunk).decode("utf-8")
                    asyncio.run_coroutine_threadsafe(
                        websocket.send_json({"type": "audio", "audio": audio_b64}), loop
                    )
            except Exception as e:
                logger.error(f"Error in text_to_speech_stream: {e}")

        await loop.run_in_executor(None, process_audio_stream)


# Global instance
elevenlabs_service = ElevenLabsService()
