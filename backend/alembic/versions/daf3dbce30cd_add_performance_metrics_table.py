"""add performance_metrics table

Revision ID: daf3dbce30cd
Revises: 978701ea2a83
Create Date: 2025-07-29 01:37:39.759881

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'daf3dbce30cd'
down_revision: Union[str, Sequence[str], None] = '978701ea2a83'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('performance_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service', sa.String(), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('latency', sa.Float(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_performance_metrics_action'), 'performance_metrics', ['action'], unique=False)
    op.create_index(op.f('ix_performance_metrics_id'), 'performance_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_performance_metrics_service'), 'performance_metrics', ['service'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_performance_metrics_service'), table_name='performance_metrics')
    op.drop_index(op.f('ix_performance_metrics_id'), table_name='performance_metrics')
    op.drop_index(op.f('ix_performance_metrics_action'), table_name='performance_metrics')
    op.drop_table('performance_metrics')
    # ### end Alembic commands ###
