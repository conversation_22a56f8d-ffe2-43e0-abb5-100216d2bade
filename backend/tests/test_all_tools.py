import asyncio
import pytest
from sqlalchemy.orm import Session
from app.database import get_db, engine
from app import models, schemas, crud
from init_db import init_database
from app.services.langchain_service import LangchainAgentService
import json

@pytest.fixture(scope="function")
def db_session():
    """Fixture to provide a database session for tests, with setup and teardown."""
    print("\n--- Setting up database for tests ---")
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)
    init_database()
    print("Sample data loaded.")
    db: Session = next(get_db())
    try:
        yield db
    finally:
        db.close()
        print("\n--- Database session closed ---")



async def run_llm_tool_call(llm_agent_service: LangchainAgentService, user_input: str, db: Session, customer_id: int = None):
    """Helper to run a tool call with context."""
    context = {}
    if customer_id:
        context = crud.get_call_context(db, customer_id)

    # The first message should be a generic system prompt
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": user_input}
    ]
    
    response = await llm_agent_service.agent_executor.ainvoke({
        "input": user_input,
        "chat_history": [
            {"role": "system", "content": "You are a helpful assistant."}
        ],
        "customer_info": context.get("customer_info", {}),
        "cart_info": context.get("cart_info", {}),
        "call_context": context.get("call_context", {}),
    })
    return response["output"]

@pytest.mark.asyncio
async def test_get_customer_by_id(db_session: Session):
    print("\n--- Testing: get_customer_by_id ---")
    llm_client = LangchainAgentService()
    result_str = await run_llm_tool_call(llm_client, "get details for customer 1", db_session, customer_id=1)
    assert "John Smith" in result_str
    print(f"Result: {result_str}")

@pytest.mark.asyncio
async def test_search_products(db_session: Session):
    print("\n--- Testing: search_products ---")
    llm_client = LangchainAgentService()
    result = await run_llm_tool_call(llm_client, "search for 'T-Shirt'", db_session)
    assert "Classic White T-Shirt" in result
    print(f"Result: {result}")

@pytest.mark.asyncio
async def test_get_cart_summary(db_session: Session):
    print("\n--- Testing: get_cart_summary ---")
    llm_client = LangchainAgentService()
    result_str = await run_llm_tool_call(llm_client, "what's in my cart?", db_session, customer_id=1)
    assert "Classic White T-Shirt" in result_str
    assert "Blue Denim Jeans" in result_str
    print(f"Result: {result_str}")

@pytest.mark.asyncio
async def test_add_item_to_cart(db_session: Session):
    print("\n--- Testing: add_cart_item_webhook ---")
    llm_client = LangchainAgentService()
    await run_llm_tool_call(llm_client, "add 1 'Leather Wallet' to my cart", db_session, customer_id=2)
    cart = crud.get_customer_active_cart(db_session, 2)
    assert any(item.product.name == "Leather Wallet" for item in cart.items)
    print(f"Cart for customer 2 now contains 'Leather Wallet'")


@pytest.mark.asyncio
async def test_remove_item_from_cart(db_session: Session):
    print("\n--- Testing: remove_cart_item ---")
    llm_client = LangchainAgentService()
    result_str = await run_llm_tool_call(llm_client, "remove 1 'blue denim jeans' from my cart", db_session, customer_id=1)
    cart = crud.get_customer_active_cart(db_session, 1)
    jeans_item = next((item for item in cart.items if item.product.name == "Blue Denim Jeans"), None)
    assert jeans_item.quantity == 1
    print(f"Result: {result_str}")

@pytest.mark.asyncio
async def test_checkout_cart(db_session: Session):
    print("\n--- Testing: checkout_cart ---")
    llm_client = LangchainAgentService()
    # Add an item to customer 3's cart before checking out
    await run_llm_tool_call(llm_client, "add 1 'Leather Wallet' to my cart", db_session, customer_id=3)
    await run_llm_tool_call(llm_client, "I'm ready to checkout", db_session, customer_id=3)
    cart = crud.get_cart(db_session, 3)
    assert cart.status == "completed"
    print(f"Cart status for customer 3 is now 'completed'")


@pytest.mark.asyncio
async def test_product_categories(db_session: Session):
    print("\n--- Testing: get_product_categories ---")
    llm_client = LangchainAgentService()
    categories_str = await run_llm_tool_call(llm_client, "what are the product categories?", db_session)
    assert "electronics" in categories_str.lower()
    assert "clothing" in categories_str.lower()
    print(f"Product categories: {categories_str}")

@pytest.mark.asyncio
async def test_search_suggestions(db_session: Session):
    print("\n--- Testing: get_search_suggestions ---")
    llm_client = LangchainAgentService()
    suggestions = await run_llm_tool_call(llm_client, "give me some search suggestions for 'sh'", db_session)
    assert "suggestion for sh" in suggestions.lower()
    print(f"Search suggestions: {suggestions}")